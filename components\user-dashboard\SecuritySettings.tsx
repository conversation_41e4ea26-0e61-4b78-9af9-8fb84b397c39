import React, { useState } from 'react';
import '../../styles/components/user-dashboard/SecuritySettings.css';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';
import DashboardModeSelector from '../DashboardModeSelector';

// Import icons
import cardCoinIcon from '../../assets/card-coin.png';
import tickCircleIcon from '../../assets/tick-circle.png';

// SVG Icon Components
const LockIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="5" y="11" width="14" height="10" rx="2" stroke="currentColor" strokeWidth="2"/>
    <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

const ArrowRightIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6 12l4-4-4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const ArrowLeftIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 4l-4 4 4 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const EyeIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1 8s3-5 7-5 7 5 7 5-3 5-7 5-7-5-7-5z" stroke="currentColor" strokeWidth="1.5"/>
    <circle cx="8" cy="8" r="2" stroke="currentColor" strokeWidth="1.5"/>
  </svg>
);

const EyeSlashIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1 8s3-5 7-5 7 5 7 5-3 5-7 5-7-5-7-5z" stroke="currentColor" strokeWidth="1.5"/>
    <circle cx="8" cy="8" r="2" stroke="currentColor" strokeWidth="1.5"/>
    <path d="M1 1l14 14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
);

type SecurityView = 'main' | 'change-password' | 'reset-password' | 'verify-email' | 'success';

const SecuritySettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('security-settings');
  const [showBalances, setShowBalances] = useState(true);
  const [hideBalances, setHideBalances] = useState(false);
  const [currentView, setCurrentView] = useState<SecurityView>('main');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  
  // Form states
  const [changePasswordForm, setChangePasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  
  const [resetPasswordForm, setResetPasswordForm] = useState({
    email: '<EMAIL>'
  });
  
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Password requirements state
  const [passwordRequirements, setPasswordRequirements] = useState({
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecialChar: false
  });

  // Toggle balance visibility
  const toggleBalances = () => {
    setShowBalances(!showBalances);
    setHideBalances(!hideBalances);
  };

  // Handle form input changes
  const handleChangePasswordInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setChangePasswordForm(prev => ({
      ...prev,
      [name]: value
    }));

    // Validate password requirements for new password
    if (name === 'newPassword') {
      setPasswordRequirements({
        minLength: value.length >= 8,
        hasUppercase: /[A-Z]/.test(value),
        hasLowercase: /[a-z]/.test(value),
        hasNumber: /\d/.test(value),
        hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(value)
      });
    }
  };

  const handleResetPasswordInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setResetPasswordForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      setVerificationCode(newCode);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  // Handle form submissions
  const handleChangePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate passwords match
    if (changePasswordForm.newPassword !== changePasswordForm.confirmPassword) {
      alert('New passwords do not match');
      return;
    }

    // Check all password requirements are met
    const allRequirementsMet = Object.values(passwordRequirements).every(req => req);
    if (!allRequirementsMet) {
      alert('Please ensure all password requirements are met');
      return;
    }

    // Simulate password change success
    setCurrentView('success');
  };

  const handleResetPasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentView('verify-email');
  };

  const handleVerifyCode = () => {
    const code = verificationCode.join('');
    if (code.length === 6) {
      // Simulate successful verification
      setCurrentView('success');
    }
  };

  const handleBackToMain = () => {
    setCurrentView('main');
    // Reset forms
    setChangePasswordForm({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setVerificationCode(['', '', '', '', '', '']);
  };

  const handleSuccessClose = () => {
    setCurrentView('main');
    setShowSuccessModal(false);
  };

  const renderMainView = () => (
    <div className="security-main-content">
      <h2 className="section-title">Security Settings</h2>
      
      <div className="security-options">
        <div className="security-option-card" onClick={() => setCurrentView('change-password')}>
          <div className="option-icon">
            <LockIcon />
          </div>
          <div className="option-content">
            <h3 className="option-title">Change Password</h3>
            <p className="option-description">Update your password to keep your account secure.</p>
          </div>
          <div className="option-arrow">
            <ArrowRightIcon />
          </div>
        </div>

        <div className="security-option-card" onClick={() => setCurrentView('reset-password')}>
          <div className="option-icon">
            <LockIcon />
          </div>
          
          <div className="option-content">
            <h3 className="option-title">Reset Password</h3>
            <p className="option-description">Forgot your password? No worries! Reset it securely.</p>
          </div>
          <div className="option-arrow">
            <ArrowRightIcon />
          </div>
        </div>
      </div>
    </div>
  );

  const renderChangePasswordView = () => (
    <div className="security-form-content">
      <div className="security-form-card">
        <button className="back-button" onClick={handleBackToMain}>
          <ArrowLeftIcon />
          Go Back
        </button>

        <div className="form-header">
          <h1 className="form-title">Change Password</h1>
          <p className="form-description">Update your password to keep your account secure.</p>
        </div>

        <form onSubmit={handleChangePasswordSubmit} className="security-form">
          <div className="form-group">
            <label className="form-label">Current Password</label>
            <div className="input-wrapper">
              <LockIcon />
              <input
                type={showCurrentPassword ? 'text' : 'password'}
                name="currentPassword"
                value={changePasswordForm.currentPassword}
                onChange={handleChangePasswordInputChange}
                placeholder="••••••••"
                className="form-input"
                required
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
              >
                {showCurrentPassword ? <EyeSlashIcon /> : <EyeIcon />}
              </button>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">New Password</label>
            <div className="input-wrapper">
              <LockIcon />
              <input
                type={showNewPassword ? 'text' : 'password'}
                name="newPassword"
                value={changePasswordForm.newPassword}
                onChange={handleChangePasswordInputChange}
                placeholder="••••••••"
                className="form-input"
                required
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowNewPassword(!showNewPassword)}
              >
                {showNewPassword ? <EyeSlashIcon /> : <EyeIcon />}
              </button>
            </div>
          </div>

        {/* Password Requirements */}
        <div className="password-requirements">
          <p className="requirements-title">Your password must:</p>
          <ul className="requirements-list">
            <li className={passwordRequirements.minLength ? 'valid' : ''}>
              Be at least 8 characters long
            </li>
            <li className={passwordRequirements.hasUppercase ? 'valid' : ''}>
              Include at least one uppercase letter (A-Z)
            </li>
            <li className={passwordRequirements.hasLowercase ? 'valid' : ''}>
              Include at least one lowercase letter (a-z)
            </li>
            <li className={passwordRequirements.hasNumber ? 'valid' : ''}>
              Include at least one number (0-9)
            </li>
            <li className={passwordRequirements.hasSpecialChar ? 'valid' : ''}>
              Include at least one special character (!@#$%^&*)
            </li>
          </ul>
        </div>

        <div className="form-group">
          <label className="form-label">Re-Enter New Password</label>
          <div className="input-wrapper">
            <LockIcon />
            <input
              type={showConfirmPassword ? 'text' : 'password'}
              name="confirmPassword"
              value={changePasswordForm.confirmPassword}
              onChange={handleChangePasswordInputChange}
              placeholder="Re-enter your new password"
              className="form-input"
              required
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? <EyeSlashIcon /> : <EyeIcon />}
            </button>
          </div>
        </div>

          <button type="submit" className="submit-button">
            Change Password
          </button>
        </form>
      </div>
    </div>
  );

  const renderResetPasswordView = () => (
    <div className="security-form-content">
      <div className="security-form-card">
        <button className="back-button" onClick={handleBackToMain}>
          <ArrowLeftIcon />
          Go Back
        </button>

        <div className="form-header">
          <h1 className="form-title">Forgot Your Password?</h1>
          <p className="form-description">We'll send you a verification code to reset your password</p>
        </div>

        <form onSubmit={handleResetPasswordSubmit} className="security-form">
          <div className="form-group">
            <label className="form-label">Email Address</label>
            <div className="input-wrapper">
              <svg className="input-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M2.5 4L8 8.5L13.5 4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <rect x="1" y="3" width="14" height="10" rx="2" stroke="currentColor" strokeWidth="1.5"/>
              </svg>
              <input
                type="email"
                name="email"
                value={resetPasswordForm.email}
                onChange={handleResetPasswordInputChange}
                className="form-input"
                required
                readOnly
              />
            </div>
          </div>

          <button type="submit" className="submit-button">
            Send Verification Code
          </button>
        </form>
      </div>
    </div>
  );

  const renderVerifyEmailView = () => (
    <div className="security-form-content">
      <div className="security-form-card">
        <button className="back-button" onClick={handleBackToMain}>
          <ArrowLeftIcon />
          Go Back
        </button>

        <div className="form-header">
          <h1 className="form-title">Verify Your Email</h1>
          <p className="form-description">
            Enter the 6-digit code sent to your email{' '}
            <strong><EMAIL></strong> to continue.
          </p>
        </div>

        <div className="verification-form">
          <div className="code-inputs">
            {verificationCode.map((digit, index) => (
              <input
                key={index}
                id={`code-${index}`}
                type="text"
                value={digit}
                onChange={(e) => handleCodeChange(index, e.target.value)}
                className="code-input"
                maxLength={1}
              />
            ))}
          </div>

          <button
            onClick={handleVerifyCode}
            className="submit-button"
            disabled={verificationCode.join('').length !== 6}
          >
            Verify Code
          </button>

          <p className="resend-text">
            Check your spam folder if you don't see it.{' '}
            <span className="resend-link">Resend Code in 139</span>
          </p>

          <button className="change-email-btn" onClick={handleBackToMain}>
            <ArrowLeftIcon />
            Change Email Address
          </button>
        </div>
      </div>
    </div>
  );

  const renderSuccessView = () => (
    <div className="security-form-content">
      <div className="security-form-card">
        <button className="back-button" onClick={handleBackToMain}>
          <ArrowLeftIcon />
          Go Back
        </button>

        <div className="form-header">
          <div className="success-icon">
            <img src={tickCircleIcon} alt="Success" />
          </div>
          <h1 className="form-title">Password Updated Successfully!</h1>
          <p className="form-description">
            Your new password has been saved. You'll use it the next time you log in.
          </p>
        </div>

        <button className="submit-button" onClick={handleSuccessClose}>
          Got It!
        </button>
      </div>
    </div>
  );

  return (
    <UserDashboardLayout
      className="security-settings-container"
      hideNavAndFooter={showSuccessModal}
    >
      <div className="security-settings-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosin 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>

            <div className="header-controls">
              <button className="stake-esvc-btn">
                <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
                Stake ESVC
              </button>

              <div className="balance-toggle">
                <span className="toggle-label">Show balances</span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={showBalances}
                    onChange={toggleBalances}
                  />
                  <span className="toggle-slider"></span>
                </label>
                <span className="toggle-label">Hide balances</span>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-layout">
          {/* Dashboard Mode Selector */}
          <div className="dashboard-mode-tabs">
            <DashboardModeSelector />
          </div>

          {/* Dashboard Content Wrapper */}
          <div className="dashboard-content-wrapper">
            {/* Sidebar */}
            <UserSideNav
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Dashboard Content */}
            <div className="dashboard-content">
            {currentView === 'main' && renderMainView()}
            {currentView === 'change-password' && renderChangePasswordView()}
            {currentView === 'reset-password' && renderResetPasswordView()}
            {currentView === 'verify-email' && renderVerifyEmailView()}
            {currentView === 'success' && renderSuccessView()}
          </div>
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  );
};

export default SecuritySettings;
