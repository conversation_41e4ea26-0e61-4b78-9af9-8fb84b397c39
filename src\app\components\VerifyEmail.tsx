'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';

interface VerifyEmailProps {
  email?: string;
}

type VerificationState = 'default' | 'focused' | 'error' | 'resent' | 'success';

const VerifyEmail: React.FC<VerifyEmailProps> = ({ email = '<EMAIL>' }) => {
  const router = useRouter();
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [currentState, setCurrentState] = useState<VerificationState>('default');
  const [resendTimer, setResendTimer] = useState(0);
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Timer countdown effect for resend
  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendTimer]);

  const handleInputChange = (index: number, value: string) => {
    if (value.length > 1) return; // Only allow single digit
    
    const newCode = [...verificationCode];
    newCode[index] = value;
    setVerificationCode(newCode);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Check if all fields are filled
    if (newCode.every(digit => digit !== '')) {
      setCurrentState('focused');
    } else if (currentState === 'error') {
      setCurrentState('default');
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleFocus = (index: number) => {
    setFocusedIndex(index);
    if (currentState === 'default') {
      setCurrentState('focused');
    }
  };

  const handleBlur = () => {
    setFocusedIndex(null);
    if (verificationCode.every(digit => digit === '')) {
      setCurrentState('default');
    }
  };

  const handleVerifyCode = () => {
    const code = verificationCode.join('');
    if (code.length !== 6) {
      setCurrentState('error');
      return;
    }

    // Simulate verification (replace with actual API call)
    if (code === '123456') {
      setCurrentState('success');
    } else {
      setCurrentState('error');
    }
  };

  const handleResendCode = () => {
    setCurrentState('resent');
    setResendTimer(60); // 1 minute timer
    setVerificationCode(['', '', '', '', '', '']);
    inputRefs.current[0]?.focus();
  };

  const handleChangeEmailAddress = () => {
    // Navigate back to signup with auto scroll to top
    router.push('/login');
  };

  const handleContinueToDashboard = () => {
    // Navigate to dashboard or main app
    router.push('/dashboard');
  };

  const getStateMessage = () => {
    switch (currentState) {
      case 'error':
        return {
          title: 'Verify Your Email to Continue',
          subtitle: `A 6-digit verification code has been sent to ${email}`,
          message: 'Please enter the code below to verify your account',
          errorText: 'Incorrect verification code entered. Please try again.'
        };
      case 'resent':
        return {
          title: 'New Email Sent',
          subtitle: `Please check your inbox [${email}] and enter the new code below to verify your account`,
          message: ''
        };
      case 'success':
        return {
          title: 'Email verified successfully!',
          subtitle: 'You can now proceed to your dashboard and start trading',
          message: ''
        };
      default:
        return {
          title: 'Verify Your Email to Continue',
          subtitle: `A 6-digit verification code has been sent to ${email}`,
          message: 'Please enter the code below to verify your account'
        };
    }
  };

  const stateMessage = getStateMessage();

  if (currentState === 'success') {
    return (
      <div className="flex items-center justify-center bg-neutral-900 py-20">
        <div className="w-full max-w-md">
          <div className="bg-[#262626] rounded-3xl p-8 shadow-xl text-center">
            <div className="mb-8">
              <div className="flex justify-center mb-6">
                <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
                  <circle cx="32" cy="32" r="32" fill="#22C55E"/>
                  <path d="M20 32L28 40L44 24" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <h2 className="text-white text-3xl font-bold mb-4 font-montserrat">{stateMessage.title}</h2>
              <p className="text-[#CCCCCC] text-lg font-medium font-montserrat">{stateMessage.subtitle}</p>
            </div>
            <button 
              className="w-full h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 flex items-center justify-center gap-2"
              onClick={handleContinueToDashboard}
            >
              Continue to Dashboard →
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center bg-neutral-900 py-20">
      <div className="w-full max-w-md">
        <div className={`bg-[#262626] rounded-3xl p-8 shadow-xl ${currentState === 'resent' ? 'border-2 border-green-500/20' : ''}`}>
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-[#BF4129] rounded-full flex items-center justify-center">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                  <polyline points="22,6 12,13 2,6"/>
                </svg>
              </div>
            </div>
            
            <h2 className="text-white text-3xl font-bold mb-4 font-montserrat">{stateMessage.title}</h2>
            <p className="text-[#CCCCCC] text-lg font-medium font-montserrat mb-2">{stateMessage.subtitle}</p>
            {stateMessage.message && <p className="text-neutral-400 text-sm font-montserrat">{stateMessage.message}</p>}
          </div>

          <div className="space-y-6">
            <div className="flex gap-3 justify-center">
              {verificationCode.map((digit, index) => (
                <input
                  key={index}
                  ref={el => { inputRefs.current[index] = el; }}
                  type="text"
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleInputChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  onFocus={() => handleFocus(index)}
                  onBlur={handleBlur}
                  className={`w-12 h-12 bg-neutral-800 border rounded-xl text-center text-white font-montserrat text-xl font-semibold transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] ${
                    currentState === 'error' ? 'border-red-500 bg-red-900/20' : 
                    currentState === 'focused' && focusedIndex === index ? 'border-[#BF4129] ring-1 ring-[#BF4129]' : 'border-neutral-700'
                  }`}
                />
              ))}
            </div>

            {currentState === 'error' && (
              <p className="text-red-400 text-sm text-center font-montserrat">{stateMessage.errorText}</p>
            )}

            <button 
              className="w-full h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 disabled:bg-neutral-600 disabled:cursor-not-allowed"
              onClick={handleVerifyCode}
              disabled={verificationCode.some(digit => digit === '')}
            >
              Verify Code
            </button>

            <div className="space-y-4">
              <div className="text-center">
                <button 
                  className="bg-transparent border-none text-[#BF4129] font-montserrat text-sm font-medium cursor-pointer underline disabled:text-neutral-500 disabled:cursor-not-allowed disabled:no-underline hover:text-[#a83a25] disabled:hover:text-neutral-500"
                  onClick={handleResendCode}
                  disabled={resendTimer > 0}
                >
                  {resendTimer > 0 ? `Resend Code (${resendTimer}s)` : 'Resend Code'}
                </button>
              </div>
              
              <div className="text-center">
                <button 
                  className="bg-transparent border-none text-[#D19049] font-montserrat text-sm font-medium cursor-pointer transition-colors duration-300 hover:text-[#BF4129] flex items-center justify-center gap-2 mx-auto"
                  onClick={handleChangeEmailAddress}
                >
                  <span>←</span> Change Email Address
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerifyEmail;
