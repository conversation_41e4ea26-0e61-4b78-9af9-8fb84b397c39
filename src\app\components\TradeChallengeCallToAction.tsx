export default function TradeChallengeCallToAction() {
  return (
    <section className="font-montserrat bg-[#262626] flex flex-col items-center gap-8 w-full mx-auto py-5 px-5 lg:px-0">
      <div className="flex flex-col items-center w-full text-center relative">
        {/* Background blur effects */}
        <div className="absolute w-[239px] h-[239px] top-0 right-0 bg-[#d19049] rounded-[119.5px] blur-[100px] opacity-30"></div>
        <div className="absolute w-[239px] h-[239px] top-[232px] left-[22px] bg-[#cc6754] rounded-[119.5px] blur-[100px] opacity-30"></div>
        
        <h1 className="text-[24px] font-bold lg:text-[40px] text-neutral-100">
          More Than Trading. Fueling Innovation
        </h1>
        <p className="max-w-[627px] font-montserrat font-normal text-neutral-300 text-base leading-6">
          Your trading journey with us is just the beginning. Stake ESVC to earn daily ROI and unlock the chance to pitch your own startup ideas for funding. We reinvest a portion of our platform's profit to support bold solutions from our staking community.
        </p>
      </div>
      
      <div className="lg:flex justify-center items-center space-y-2 gap-4 mt-10">
        <button className="w-full flex items-center bg-[#bf4129] hover:bg-[#a83a25] text-white py-3 px-6 rounded-full font-semibold text-lg transition-colors duration-200 shadow-lg whitespace-nowrap">
          Start Staking Now 
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-2 w-5 h-5">
            <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"></path>
            <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"></path>
            <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></path>
            <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path>
          </svg>
        </button>
        <button className="w-full bg-transparent text-neutral-300 border border-neutral-300 py-3 px-6 rounded-full font-semibold text-lg hover:border-[#C6741B] hover:text-[#C6741B] transition-colors duration-200">
          Get Funded
        </button>
      </div>
    </section>
  );
}
