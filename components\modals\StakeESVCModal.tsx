import React, { useState, useEffect, useRef } from 'react';
import QRCode from 'qrcode';
import '../../styles/components/modals/StakeESVCModal.css';

// Import icons
import arrow3Icon from '../../assets/arrow-3.png';

// Copy SVG Component
const CopyIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
  </svg>
);

interface StakeESVCModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type ModalStep = 'stake-form' | 'payment-currency' | 'payment-qr' | 'success';

const StakeESVCModal: React.FC<StakeESVCModalProps> = ({ isOpen, onClose }) => {
  const [currentStep, setCurrentStep] = useState<ModalStep>('stake-form');
  const [stakeAmount, setStakeAmount] = useState('');
  const [esvcReceived, setEsvcReceived] = useState('');
  const [stakingDuration, setStakingDuration] = useState('6');
  const [walletAddress, setWalletAddress] = useState('');
  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [countdown, setCountdown] = useState(1799); // 29:59 in seconds
  const qrCanvasRef = useRef<HTMLCanvasElement>(null);

  // Calculate ESVC received based on stake amount
  useEffect(() => {
    if (stakeAmount) {
      const amount = parseFloat(stakeAmount);
      if (!isNaN(amount)) {
        setEsvcReceived((amount * 1.25).toString()); // Example conversion rate
      }
    }
  }, [stakeAmount]);

  // Countdown timer for payment
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (currentStep === 'payment-qr' && countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [currentStep, countdown]);

  // Generate QR code when payment step is reached
  useEffect(() => {
    if (currentStep === 'payment-qr' && qrCanvasRef.current && walletAddress) {
      QRCode.toCanvas(qrCanvasRef.current, walletAddress, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      }, (error) => {
        if (error) console.error('QR Code generation error:', error);
      });
    }
  }, [currentStep, walletAddress]);

  // Format countdown time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStakeFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentStep('payment-currency');
  };

  const handleCurrencySelect = (currency: string) => {
    setSelectedCurrency(currency);
    setCurrentStep('payment-qr');
  };

  const handlePaymentComplete = () => {
    setCurrentStep('success');
  };

  const handleCloseModal = () => {
    setCurrentStep('stake-form');
    setStakeAmount('');
    setEsvcReceived('');
    setStakingDuration('6');
    setWalletAddress('');
    setSelectedCurrency('');
    setCountdown(1799);
    onClose();
  };

  const handleProceedToStakingDashboard = () => {
    handleCloseModal();
    // Navigate to staking dashboard
  };

  if (!isOpen) return null;

  return (
    <div className="stake-modal-overlay">
      <div className="stake-modal">
        <button className="stake-modal-close" onClick={handleCloseModal}>
          ×
        </button>

        {currentStep === 'stake-form' && (
          <div className="stake-form-content">
            <h2 className="stake-modal-title">Stake ESVC</h2>
            
            <form onSubmit={handleStakeFormSubmit}>
              <div className="stake-form-group">
                <label className="stake-form-label">How much would you like to stake? (In USD)</label>
                <div className="stake-amount-input">
                  <span className="currency-symbol">$</span>
                  <input
                    type="number"
                    value={stakeAmount}
                    onChange={(e) => setStakeAmount(e.target.value)}
                    className="stake-input"
                    placeholder="0"
                    required
                  />
                </div>
              </div>

              <div className="stake-form-group">
                <label className="stake-form-label">You will receive</label>
                <div className="esvc-received">
                  <span className="esvc-amount">{esvcReceived || '0'}</span>
                  <span className="esvc-label">ESVC</span>
                  <div className="swap-icon">
                    <img src={arrow3Icon} alt="Swap" className="swap-arrow" />
                  </div>
                </div>
              </div>

              <div className="stake-form-group">
                <label className="stake-form-label">Select Staking Duration</label>
                <div className="duration-options">
                  <label className="duration-option">
                    <input
                      type="radio"
                      name="duration"
                      value="6"
                      checked={stakingDuration === '6'}
                      onChange={(e) => setStakingDuration(e.target.value)}
                    />
                    <span className="duration-text">6 months</span>
                  </label>
                  <label className="duration-option">
                    <input
                      type="radio"
                      name="duration"
                      value="12"
                      checked={stakingDuration === '12'}
                      onChange={(e) => setStakingDuration(e.target.value)}
                    />
                    <span className="duration-text">12 months</span>
                  </label>
                </div>
              </div>

              <div className="stake-form-group">
                <label className="stake-form-label">Enter your USDC wallet address for payout</label>
                <p className="stake-form-description">
                  We'll use this wallet to send your daily ROI payouts. You can update it anytime before withdrawing.
                </p>
                <input
                  type="text"
                  value={walletAddress}
                  onChange={(e) => setWalletAddress(e.target.value)}
                  className="stake-input"
                  placeholder="0x0F2gVzWxMqMzrGpyMWnZRpZBuAkKFef"
                  required
                />
                <div className="wallet-info">
                  <span className="info-icon">ⓘ</span>
                  <span className="info-text">Ensure your wallet supports USDC (Solana network)</span>
                </div>
              </div>

              <button type="submit" className="proceed-payment-btn">
                Proceed to Payment
              </button>
            </form>
          </div>
        )}

        {currentStep === 'payment-currency' && (
          <div className="payment-currency-content">
            <button className="back-btn" onClick={() => setCurrentStep('stake-form')}>
              ← Select Payment Currency
            </button>
            
            <div className="currency-selection">
              <label className="currency-label">Choose your deposit currency</label>
              <select 
                className="currency-select"
                value={selectedCurrency}
                onChange={(e) => setSelectedCurrency(e.target.value)}
              >
                <option value="">Click to select</option>
                <option value="SOL">Solana (SOL)</option>
                <option value="USDC">USDC</option>
                <option value="BTC">Bitcoin (BTC)</option>
              </select>

              <div className="payment-warning">
                <span className="warning-icon">⚠️</span>
                <div className="warning-text">
                  <strong>Important! Please Read Before You Pay</strong>
                  <p>Only send USDC or Solana depending on the option you selected.</p>
                  <p>If you send any other token or from the wrong network, your money will be lost and cannot be recovered.</p>
                </div>
              </div>

              <button 
                className="proceed-payment-btn"
                onClick={() => handleCurrencySelect(selectedCurrency)}
                disabled={!selectedCurrency}
              >
                Proceed to Payment
              </button>
            </div>
          </div>
        )}

        {currentStep === 'payment-qr' && (
          <div className="payment-qr-content">
            <button className="back-btn" onClick={() => setCurrentStep('payment-currency')}>
              ← Select Payment Currency
            </button>

            <div className="qr-payment-section">
              <div className="currency-display">
                <span className="selected-currency">{selectedCurrency}</span>
              </div>

              <div className="qr-code-container">
                <div className="qr-code">
                  <canvas
                    ref={qrCanvasRef}
                    className="qr-canvas"
                  />
                </div>
              </div>

              <div className="wallet-address-section">
                <label className="wallet-label">Solana Wallet Address</label>
                <div className="wallet-address-display">
                  <span className="wallet-address">exeFz9gVzWxMqMzrGpyMWnZRpZBuAkKFef</span>
                  <button
                    className="copy-btn"
                    onClick={() => {
                      navigator.clipboard.writeText('exeFz9gVzWxMqMzrGpyMWnZRpZBuAkKFef');
                    }}
                  >
                    <CopyIcon />
                    Copy
                  </button>
                </div>
              </div>

              <div className="payment-timer">
                <span className="timer-display">{formatTime(countdown)}</span>
              </div>

              <div className="payment-info">
                <span className="info-icon">ⓘ</span>
                <span className="info-text">Do not close this page until deposit is complete.</span>
              </div>

              <button className="deposit-complete-btn" onClick={handlePaymentComplete}>
                I Have Deposited
              </button>
            </div>
          </div>
        )}

        {currentStep === 'success' && (
          <div className="success-content">
            <div className="success-header">
              <div className="success-icon"></div>
              <h2 className="success-title">Congratulations!</h2>
              <p className="success-subtitle">
                Your account has been credited with {esvcReceived} ESVC,
                automatically staked for {stakingDuration} months.
              </p>
            </div>

            <div className="staking-summary">
              <h3 className="summary-title">Staking Summary</h3>
              
              <div className="summary-row">
                <span className="summary-label">Number of ESVC staked</span>
                <span className="summary-value">1250 ESVC</span>
              </div>
              
              <div className="summary-row">
                <span className="summary-label">Value at time of staking (USD)</span>
                <span className="summary-value">$1,000</span>
              </div>
              
              <div className="summary-row">
                <span className="summary-label">Expected ROI</span>
                <span className="summary-value">$2,000</span>
              </div>
              
              <div className="summary-row">
                <span className="summary-label">Lock Period</span>
                <span className="summary-value">12 months</span>
              </div>
              
              <div className="summary-row">
                <span className="summary-label">ROI Earned So Far</span>
                <span className="summary-value">$0.00</span>
              </div>
              
              <div className="summary-row">
                <span className="summary-label">Days Left</span>
                <span className="summary-value">365 days</span>
              </div>
              
              <div className="summary-row">
                <span className="summary-label">Date Staked</span>
                <span className="summary-value">Jan 3, 2025</span>
              </div>
              
              <div className="summary-row">
                <span className="summary-label">Unstake Date</span>
                <span className="summary-value">Jan 3, 2026</span>
              </div>
            </div>

            <button className="proceed-dashboard-btn" onClick={handleProceedToStakingDashboard}>
              Proceed to Staking Dashboard
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default StakeESVCModal;
