'use client';

import React, { useState, useEffect } from 'react';

const RealTimeStaking: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [stakingData, setStakingData] = useState({
    totalStaked: 850000,
    totalStakers: 1247,
    averageStake: 682,
    rewardsDistributed: 45230
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
      // Simulate real-time data updates
      setStakingData(prev => ({
        ...prev,
        totalStaked: prev.totalStaked + Math.floor(Math.random() * 100),
        rewardsDistributed: prev.rewardsDistributed + Math.floor(Math.random() * 10)
      }));
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="space-y-8">
      {/* Real-time Status */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-white text-2xl font-bold font-montserrat">Real-Time Staking Overview</h2>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-green-400 font-montserrat font-medium">Live Data</span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Total ESVC Staked</h3>
            <p className="text-white text-3xl font-bold font-montserrat">{stakingData.totalStaked.toLocaleString()}</p>
            <p className="text-green-400 text-sm font-montserrat mt-2">68% of supply</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Active Stakers</h3>
            <p className="text-white text-3xl font-bold font-montserrat">{stakingData.totalStakers.toLocaleString()}</p>
            <p className="text-blue-400 text-sm font-montserrat mt-2">+89 this week</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Average Stake</h3>
            <p className="text-white text-3xl font-bold font-montserrat">{stakingData.averageStake}</p>
            <p className="text-purple-400 text-sm font-montserrat mt-2">ESVC per user</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Rewards Distributed</h3>
            <p className="text-white text-3xl font-bold font-montserrat">${stakingData.rewardsDistributed.toLocaleString()}</p>
            <p className="text-yellow-400 text-sm font-montserrat mt-2">Today</p>
          </div>
        </div>
      </div>

      {/* Staking Tiers */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Staking Tiers & Rewards</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Bronze Tier */}
          <div className="bg-gradient-to-br from-amber-900/20 to-amber-700/20 border border-amber-700/30 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">B</span>
              </div>
              <h3 className="text-amber-400 text-lg font-bold font-montserrat">Bronze Tier</h3>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">Minimum Stake:</span>
                <span className="text-white font-montserrat font-semibold">100 ESVC</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">APY:</span>
                <span className="text-amber-400 font-montserrat font-semibold">8%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">Active Stakers:</span>
                <span className="text-white font-montserrat font-semibold">892</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">Total Staked:</span>
                <span className="text-white font-montserrat font-semibold">245K ESVC</span>
              </div>
            </div>
          </div>

          {/* Silver Tier */}
          <div className="bg-gradient-to-br from-gray-600/20 to-gray-400/20 border border-gray-400/30 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">S</span>
              </div>
              <h3 className="text-gray-300 text-lg font-bold font-montserrat">Silver Tier</h3>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">Minimum Stake:</span>
                <span className="text-white font-montserrat font-semibold">500 ESVC</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">APY:</span>
                <span className="text-gray-300 font-montserrat font-semibold">12%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">Active Stakers:</span>
                <span className="text-white font-montserrat font-semibold">285</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">Total Staked:</span>
                <span className="text-white font-montserrat font-semibold">380K ESVC</span>
              </div>
            </div>
          </div>

          {/* Gold Tier */}
          <div className="bg-gradient-to-br from-yellow-600/20 to-yellow-400/20 border border-yellow-400/30 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">G</span>
              </div>
              <h3 className="text-yellow-400 text-lg font-bold font-montserrat">Gold Tier</h3>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">Minimum Stake:</span>
                <span className="text-white font-montserrat font-semibold">1,000 ESVC</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">APY:</span>
                <span className="text-yellow-400 font-montserrat font-semibold">18%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">Active Stakers:</span>
                <span className="text-white font-montserrat font-semibold">70</span>
              </div>
              <div className="flex justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">Total Staked:</span>
                <span className="text-white font-montserrat font-semibold">225K ESVC</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Staking Activity */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Recent Staking Activity</h2>
        
        <div className="space-y-4">
          {[
            { type: 'stake', user: '0x742d...8D4C', amount: '1,500 ESVC', time: '2 minutes ago', tier: 'Gold' },
            { type: 'reward', user: '0x8D4C...25a3', amount: '45 ESVC', time: '5 minutes ago', tier: 'Silver' },
            { type: 'stake', user: '0x925a...5329', amount: '250 ESVC', time: '8 minutes ago', tier: 'Bronze' },
            { type: 'unstake', user: '0x532a...4C05', amount: '800 ESVC', time: '12 minutes ago', tier: 'Silver' },
            { type: 'reward', user: '0x35Cc...0532', amount: '120 ESVC', time: '15 minutes ago', tier: 'Gold' },
            { type: 'stake', user: '0x6634...925a', amount: '2,000 ESVC', time: '18 minutes ago', tier: 'Gold' }
          ].map((activity, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-neutral-800 rounded-xl">
              <div className="flex items-center gap-4">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  activity.type === 'stake' ? 'bg-green-500/20' :
                  activity.type === 'unstake' ? 'bg-red-500/20' : 'bg-blue-500/20'
                }`}>
                  <span className={`text-sm font-bold ${
                    activity.type === 'stake' ? 'text-green-400' :
                    activity.type === 'unstake' ? 'text-red-400' : 'text-blue-400'
                  }`}>
                    {activity.type === 'stake' ? '+' : activity.type === 'unstake' ? '-' : '★'}
                  </span>
                </div>
                
                <div>
                  <p className="text-white font-montserrat font-semibold">
                    {activity.type === 'stake' ? 'New Stake' : 
                     activity.type === 'unstake' ? 'Unstaked' : 'Reward Claimed'}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <p className="text-neutral-400 text-sm font-montserrat font-mono">{activity.user}</p>
                    <span className="text-neutral-500">•</span>
                    <div className={`px-2 py-1 rounded-full text-xs font-montserrat ${
                      activity.tier === 'Gold' ? 'bg-yellow-500/20 text-yellow-400' :
                      activity.tier === 'Silver' ? 'bg-gray-400/20 text-gray-300' :
                      'bg-amber-600/20 text-amber-400'
                    }`}>
                      {activity.tier}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <p className={`font-montserrat font-bold ${
                  activity.type === 'stake' ? 'text-green-400' :
                  activity.type === 'unstake' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {activity.type === 'unstake' ? '-' : '+'}{activity.amount}
                </p>
                <p className="text-neutral-400 text-sm font-montserrat">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Staking Pool Health */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Pool Health Metrics</h3>
          
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-neutral-300 font-montserrat text-sm">Staking Ratio</span>
                <span className="text-white font-montserrat font-semibold">68%</span>
              </div>
              <div className="w-full bg-neutral-700 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '68%' }}></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-neutral-300 font-montserrat text-sm">Reward Pool</span>
                <span className="text-white font-montserrat font-semibold">85%</span>
              </div>
              <div className="w-full bg-neutral-700 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: '85%' }}></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-neutral-300 font-montserrat text-sm">Liquidity</span>
                <span className="text-white font-montserrat font-semibold">92%</span>
              </div>
              <div className="w-full bg-neutral-700 rounded-full h-2">
                <div className="bg-purple-500 h-2 rounded-full" style={{ width: '92%' }}></div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Reward Distribution</h3>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-neutral-300 font-montserrat text-sm">Next Distribution:</span>
              <span className="text-white font-montserrat font-semibold">2h 34m</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-neutral-300 font-montserrat text-sm">Pending Rewards:</span>
              <span className="text-green-400 font-montserrat font-semibold">$12,450</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-neutral-300 font-montserrat text-sm">Distribution Frequency:</span>
              <span className="text-white font-montserrat font-semibold">Every 4 hours</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-neutral-300 font-montserrat text-sm">Last Distribution:</span>
              <span className="text-white font-montserrat font-semibold">1h 26m ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeStaking;
