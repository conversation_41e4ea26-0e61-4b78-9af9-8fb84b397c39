'use client';

import React, { useState, useEffect } from 'react';

const LiveReserve: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="space-y-8">
      {/* Live Status Header */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-white text-2xl font-bold font-montserrat">Live Reserve Status</h2>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-green-400 font-montserrat font-medium">Live</span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <p className="text-neutral-400 text-sm font-montserrat mb-2">Last Updated</p>
            <p className="text-white text-lg font-bold font-montserrat">
              {currentTime.toLocaleTimeString()}
            </p>
          </div>
          
          <div className="text-center">
            <p className="text-neutral-400 text-sm font-montserrat mb-2">Update Frequency</p>
            <p className="text-white text-lg font-bold font-montserrat">Real-time</p>
          </div>
          
          <div className="text-center">
            <p className="text-neutral-400 text-sm font-montserrat mb-2">Data Source</p>
            <p className="text-white text-lg font-bold font-montserrat">On-chain</p>
          </div>
        </div>
      </div>

      {/* Reserve Holdings */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Current Holdings</h2>
        
        <div className="space-y-6">
          {/* Bitcoin */}
          <div className="bg-neutral-800 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">₿</span>
                </div>
                <div>
                  <h3 className="text-white text-lg font-semibold font-montserrat">Bitcoin</h3>
                  <p className="text-neutral-400 text-sm font-montserrat">BTC</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white text-xl font-bold font-montserrat">22.45 BTC</p>
                <p className="text-green-400 text-sm font-montserrat">+2.3% (24h)</p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">USD Value</p>
                <p className="text-white font-montserrat font-semibold">$960,450</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">% of Portfolio</p>
                <p className="text-white font-montserrat font-semibold">40.2%</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">Avg. Buy Price</p>
                <p className="text-white font-montserrat font-semibold">$41,200</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">Unrealized P&L</p>
                <p className="text-green-400 font-montserrat font-semibold">+$125,890</p>
              </div>
            </div>
          </div>

          {/* Ethereum */}
          <div className="bg-neutral-800 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">Ξ</span>
                </div>
                <div>
                  <h3 className="text-white text-lg font-semibold font-montserrat">Ethereum</h3>
                  <p className="text-neutral-400 text-sm font-montserrat">ETH</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white text-xl font-bold font-montserrat">285.7 ETH</p>
                <p className="text-green-400 text-sm font-montserrat">+1.8% (24h)</p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">USD Value</p>
                <p className="text-white font-montserrat font-semibold">$720,280</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">% of Portfolio</p>
                <p className="text-white font-montserrat font-semibold">30.1%</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">Avg. Buy Price</p>
                <p className="text-white font-montserrat font-semibold">$2,380</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">Unrealized P&L</p>
                <p className="text-green-400 font-montserrat font-semibold">+$40,120</p>
              </div>
            </div>
          </div>

          {/* USDC */}
          <div className="bg-neutral-800 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">$</span>
                </div>
                <div>
                  <h3 className="text-white text-lg font-semibold font-montserrat">USD Coin</h3>
                  <p className="text-neutral-400 text-sm font-montserrat">USDC</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white text-xl font-bold font-montserrat">480,000 USDC</p>
                <p className="text-neutral-400 text-sm font-montserrat">0.0% (24h)</p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">USD Value</p>
                <p className="text-white font-montserrat font-semibold">$480,000</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">% of Portfolio</p>
                <p className="text-white font-montserrat font-semibold">20.0%</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">Purpose</p>
                <p className="text-white font-montserrat font-semibold">Liquidity</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">Yield</p>
                <p className="text-blue-400 font-montserrat font-semibold">4.2% APY</p>
              </div>
            </div>
          </div>

          {/* Other Assets */}
          <div className="bg-neutral-800 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">+</span>
                </div>
                <div>
                  <h3 className="text-white text-lg font-semibold font-montserrat">Other Assets</h3>
                  <p className="text-neutral-400 text-sm font-montserrat">Various</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white text-xl font-bold font-montserrat">Mixed</p>
                <p className="text-green-400 text-sm font-montserrat">+3.1% (24h)</p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">USD Value</p>
                <p className="text-white font-montserrat font-semibold">$240,270</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">% of Portfolio</p>
                <p className="text-white font-montserrat font-semibold">10.0%</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">Assets</p>
                <p className="text-white font-montserrat font-semibold">12 Types</p>
              </div>
              <div>
                <p className="text-neutral-400 text-xs font-montserrat">Avg. Return</p>
                <p className="text-green-400 font-montserrat font-semibold">+8.5%</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Reserve Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Total Reserve Value</h3>
          <p className="text-white text-3xl font-bold font-montserrat mb-2">$2,401,000</p>
          <div className="flex items-center gap-2">
            <span className="text-green-400 text-sm font-montserrat">+$45,230</span>
            <span className="text-neutral-400 text-sm font-montserrat">(+1.9% today)</span>
          </div>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Available Liquidity</h3>
          <p className="text-white text-3xl font-bold font-montserrat mb-2">$680,000</p>
          <div className="flex items-center gap-2">
            <span className="text-blue-400 text-sm font-montserrat">28.3%</span>
            <span className="text-neutral-400 text-sm font-montserrat">of total</span>
          </div>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">24h Change</h3>
          <p className="text-green-400 text-3xl font-bold font-montserrat mb-2">+1.9%</p>
          <div className="flex items-center gap-2">
            <span className="text-green-400 text-sm font-montserrat">+$45,230</span>
            <span className="text-neutral-400 text-sm font-montserrat">USD</span>
          </div>
        </div>
      </div>

      {/* Wallet Addresses */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Treasury Wallet Addresses</h2>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-neutral-800 rounded-xl">
            <div>
              <p className="text-white font-montserrat font-semibold">Main Treasury (Multi-sig)</p>
              <p className="text-neutral-400 text-sm font-montserrat font-mono">******************************************</p>
            </div>
            <button className="text-[#BF4129] hover:text-[#a83a25] font-montserrat text-sm font-medium transition-colors duration-300">
              View on Explorer →
            </button>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-neutral-800 rounded-xl">
            <div>
              <p className="text-white font-montserrat font-semibold">Staking Rewards Pool</p>
              <p className="text-neutral-400 text-sm font-montserrat font-mono">******************************************</p>
            </div>
            <button className="text-[#BF4129] hover:text-[#a83a25] font-montserrat text-sm font-medium transition-colors duration-300">
              View on Explorer →
            </button>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-neutral-800 rounded-xl">
            <div>
              <p className="text-white font-montserrat font-semibold">Startup Funding Vault</p>
              <p className="text-neutral-400 text-sm font-montserrat font-mono">0x925a3b8D4742d35Cc6634C0532925a3b8D4C05329</p>
            </div>
            <button className="text-[#BF4129] hover:text-[#a83a25] font-montserrat text-sm font-medium transition-colors duration-300">
              View on Explorer →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveReserve;
