'use client';

import React, { useState } from 'react';

const ROIDistribution: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');

  const distributionData = {
    monthly: {
      totalDistributed: '$28,450',
      recipients: 1247,
      averageROI: '2.3%',
      nextDistribution: '3 days'
    },
    quarterly: {
      totalDistributed: '$85,350',
      recipients: 1247,
      averageROI: '6.9%',
      nextDistribution: '45 days'
    },
    yearly: {
      totalDistributed: '$341,400',
      recipients: 1247,
      averageROI: '27.6%',
      nextDistribution: '320 days'
    }
  };

  const currentData = distributionData[selectedPeriod as keyof typeof distributionData];

  const recentDistributions = [
    {
      id: 1,
      date: '2024-01-15',
      amount: '$28,450',
      recipients: 1247,
      source: 'Trading Bot Revenue',
      status: 'Completed'
    },
    {
      id: 2,
      date: '2023-12-15',
      amount: '$26,890',
      recipients: 1198,
      source: 'Staking Rewards',
      status: 'Completed'
    },
    {
      id: 3,
      date: '2023-11-15',
      amount: '$31,200',
      recipients: 1156,
      source: 'Investment Returns',
      status: 'Completed'
    },
    {
      id: 4,
      date: '2023-10-15',
      amount: '$29,750',
      recipients: 1089,
      source: 'Trading Bot Revenue',
      status: 'Completed'
    }
  ];

  const stakingTiers = [
    {
      tier: 'Gold',
      minStake: '1,000+ ESVC',
      roiRate: '18%',
      holders: 70,
      totalDistributed: '$12,450',
      color: 'bg-yellow-500'
    },
    {
      tier: 'Silver',
      minStake: '500-999 ESVC',
      roiRate: '12%',
      holders: 285,
      totalDistributed: '$10,200',
      color: 'bg-gray-400'
    },
    {
      tier: 'Bronze',
      minStake: '100-499 ESVC',
      roiRate: '8%',
      holders: 892,
      totalDistributed: '$5,800',
      color: 'bg-amber-600'
    }
  ];

  return (
    <div className="space-y-8">
      {/* ROI Overview */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-6 mb-6">
          <h2 className="text-white text-2xl font-bold font-montserrat">ROI Distribution Overview</h2>
          
          <div className="flex bg-neutral-800 rounded-lg p-1">
            {[
              { id: 'monthly', label: 'Monthly' },
              { id: 'quarterly', label: 'Quarterly' },
              { id: 'yearly', label: 'Yearly' }
            ].map((period) => (
              <button
                key={period.id}
                onClick={() => setSelectedPeriod(period.id)}
                className={`px-4 py-2 rounded-md font-montserrat text-sm font-medium transition-all duration-300 ${
                  selectedPeriod === period.id
                    ? 'bg-[#BF4129] text-white'
                    : 'text-neutral-300 hover:text-white'
                }`}
              >
                {period.label}
              </button>
            ))}
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Total Distributed</h3>
            <p className="text-white text-3xl font-bold font-montserrat">{currentData.totalDistributed}</p>
            <p className="text-green-400 text-sm font-montserrat mt-2">{selectedPeriod}</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Recipients</h3>
            <p className="text-white text-3xl font-bold font-montserrat">{currentData.recipients.toLocaleString()}</p>
            <p className="text-blue-400 text-sm font-montserrat mt-2">Active stakers</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Average ROI</h3>
            <p className="text-white text-3xl font-bold font-montserrat">{currentData.averageROI}</p>
            <p className="text-purple-400 text-sm font-montserrat mt-2">Per staker</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Next Distribution</h3>
            <p className="text-white text-3xl font-bold font-montserrat">{currentData.nextDistribution}</p>
            <p className="text-yellow-400 text-sm font-montserrat mt-2">Estimated</p>
          </div>
        </div>
      </div>

      {/* ROI by Staking Tier */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">ROI Distribution by Staking Tier</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {stakingTiers.map((tier, index) => (
            <div key={index} className="bg-neutral-800 rounded-xl p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className={`w-4 h-4 rounded-full ${tier.color}`}></div>
                <h3 className="text-white text-lg font-bold font-montserrat">{tier.tier} Tier</h3>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-neutral-400 font-montserrat text-sm">Minimum Stake:</span>
                  <span className="text-white font-montserrat font-semibold text-sm">{tier.minStake}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-neutral-400 font-montserrat text-sm">ROI Rate:</span>
                  <span className="text-green-400 font-montserrat font-semibold text-sm">{tier.roiRate} APY</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-neutral-400 font-montserrat text-sm">Holders:</span>
                  <span className="text-white font-montserrat font-semibold text-sm">{tier.holders}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-neutral-400 font-montserrat text-sm">Distributed:</span>
                  <span className="text-[#BF4129] font-montserrat font-semibold text-sm">{tier.totalDistributed}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Distributions */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Recent Distribution History</h2>
        
        <div className="space-y-4">
          {recentDistributions.map((distribution) => (
            <div key={distribution.id} className="bg-neutral-800 rounded-xl p-6">
              <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-2">
                    <h3 className="text-white text-lg font-semibold font-montserrat">
                      Distribution #{distribution.id}
                    </h3>
                    <div className="px-3 py-1 rounded-full text-xs font-montserrat bg-green-500/20 text-green-400">
                      {distribution.status}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-neutral-400 font-montserrat">Date:</span>
                      <span className="text-white font-montserrat ml-2">{distribution.date}</span>
                    </div>
                    <div>
                      <span className="text-neutral-400 font-montserrat">Recipients:</span>
                      <span className="text-white font-montserrat ml-2">{distribution.recipients.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="text-neutral-400 font-montserrat">Source:</span>
                      <span className="text-white font-montserrat ml-2">{distribution.source}</span>
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <p className="text-green-400 text-2xl font-bold font-montserrat">{distribution.amount}</p>
                  <button className="text-[#BF4129] hover:text-[#a83a25] text-sm font-montserrat font-medium transition-colors duration-300 mt-1">
                    View Details →
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Distribution Analytics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Distribution Sources</h3>
          
          <div className="space-y-4">
            {[
              { source: 'Trading Bot Revenue', amount: '$45,230', percentage: 45, color: 'bg-[#BF4129]' },
              { source: 'Staking Rewards', amount: '$28,450', percentage: 28, color: 'bg-blue-500' },
              { source: 'Investment Returns', amount: '$18,920', percentage: 19, color: 'bg-green-500' },
              { source: 'Platform Fees', amount: '$8,400', percentage: 8, color: 'bg-purple-500' }
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                  <span className="text-neutral-300 font-montserrat text-sm">{item.source}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-20 bg-neutral-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${item.color}`} 
                      style={{ width: `${item.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-white font-montserrat font-semibold text-sm w-16 text-right">
                    {item.amount}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Distribution Schedule</h3>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-neutral-800 rounded-lg">
              <div>
                <p className="text-white font-montserrat font-semibold">Monthly Distribution</p>
                <p className="text-neutral-400 text-sm font-montserrat">15th of each month</p>
              </div>
              <div className="text-right">
                <p className="text-green-400 font-montserrat font-semibold">Active</p>
                <p className="text-neutral-400 text-sm font-montserrat">Next: Jan 15</p>
              </div>
            </div>
            
            <div className="flex justify-between items-center p-3 bg-neutral-800 rounded-lg">
              <div>
                <p className="text-white font-montserrat font-semibold">Quarterly Bonus</p>
                <p className="text-neutral-400 text-sm font-montserrat">End of each quarter</p>
              </div>
              <div className="text-right">
                <p className="text-blue-400 font-montserrat font-semibold">Scheduled</p>
                <p className="text-neutral-400 text-sm font-montserrat">Next: Mar 31</p>
              </div>
            </div>
            
            <div className="flex justify-between items-center p-3 bg-neutral-800 rounded-lg">
              <div>
                <p className="text-white font-montserrat font-semibold">Annual Rewards</p>
                <p className="text-neutral-400 text-sm font-montserrat">December 31st</p>
              </div>
              <div className="text-right">
                <p className="text-purple-400 font-montserrat font-semibold">Planned</p>
                <p className="text-neutral-400 text-sm font-montserrat">Next: Dec 31</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* ROI Calculator */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">ROI Calculator</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Calculate Your Potential ROI</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-neutral-400 text-sm font-montserrat mb-2">Stake Amount (ESVC)</label>
                <input
                  type="number"
                  placeholder="1000"
                  className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-lg px-4 text-white font-montserrat focus:outline-none focus:border-[#BF4129]"
                />
              </div>
              
              <div>
                <label className="block text-neutral-400 text-sm font-montserrat mb-2">Time Period</label>
                <select className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-lg px-4 text-white font-montserrat focus:outline-none focus:border-[#BF4129]">
                  <option value="monthly">Monthly</option>
                  <option value="quarterly">Quarterly</option>
                  <option value="yearly">Yearly</option>
                </select>
              </div>
              
              <button className="w-full h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-lg text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300">
                Calculate ROI
              </button>
            </div>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-6">
            <h4 className="text-white font-montserrat font-semibold mb-4">Estimated Returns</h4>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-neutral-400 font-montserrat text-sm">Stake Amount:</span>
                <span className="text-white font-montserrat font-semibold">1,000 ESVC</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-neutral-400 font-montserrat text-sm">Tier:</span>
                <span className="text-yellow-400 font-montserrat font-semibold">Gold (18% APY)</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-neutral-400 font-montserrat text-sm">Monthly ROI:</span>
                <span className="text-green-400 font-montserrat font-semibold">15 ESVC</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-neutral-400 font-montserrat text-sm">Yearly ROI:</span>
                <span className="text-green-400 font-montserrat font-semibold">180 ESVC</span>
              </div>
              
              <div className="pt-3 border-t border-neutral-700">
                <div className="flex justify-between">
                  <span className="text-white font-montserrat font-semibold">Total Value (1 year):</span>
                  <span className="text-[#BF4129] font-montserrat font-bold">1,180 ESVC</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ROIDistribution;
