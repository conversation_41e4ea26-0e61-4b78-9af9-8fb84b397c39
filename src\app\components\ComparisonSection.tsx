import Image from "next/image";

export default function ComparisonSection() {
  return (
    <section className="relative w-full">
      {/* Background blur effects */}
      <div className="absolute w-[239px] h-[239px] top-0 lg:right-0 rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>
      
      <div className="mx-auto max-w-[1082px] relative">
        <div className="absolute w-[381px] h-[381px] top-20 left-0 bg-[#d19049] rounded-[190.5px] blur-[159.41px] opacity-20"></div>
        
        <section className="flex flex-col w-full max-w-[880px] items-center gap-10 mx-auto my-10">
          <div className="flex flex-col items-center gap-10 w-full">
            <div className="flex flex-col w-full max-w-[517px] items-center gap-4">
              <h2 className="w-full font-montserrat font-semibold text-neutral-100 text-[40px] text-center">
                What Makes Us Different
              </h2>
              <p className="w-full font-montserrat font-normal text-neutral-300 text-base text-center">
                ESVC Staking Versus Traditional Crypto
              </p>
            </div>
            
            <div className="flex w-full px-5 md:px-0">
              {/* Traditional Crypto Column */}
              <div className="flex flex-col items-start gap-1 flex-1">
                <div className="flex items-center justify-center gap-2.5 md:px-[76px] py-2.5 w-full bg-neutral-800 rounded-[16px_0px_0px_0px]">
                  <h3 className="font-montserrat font-semibold text-neutral-100 md:text-2xl text-center">
                    Traditional Crypto
                  </h3>
                </div>
                <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]">
                  <span className="font-montserrat font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">
                    Speculative Trading
                  </span>
                </div>
                <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]">
                  <span className="font-montserrat font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">
                    No revenue backing
                  </span>
                </div>
                <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]">
                  <span className="font-montserrat font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">
                    Opaque Tokenomics
                  </span>
                </div>
                <div className="md:flex items-center gap-3 px-5 py-2.5 w-full rounded-bl-2xl border">
                  <span className="font-montserrat font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">
                    Low staking rewards
                  </span>
                </div>
              </div>
              
              {/* ESVC Column */}
              <div className="flex flex-col items-start gap-1 flex-1">
                <div className="flex items-center justify-center gap-2.5 md:px-[76px] py-2.5 w-full rounded-[0px_16px_0px_0px] bg-[linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%),linear-gradient(0deg,rgba(38,38,38,1)_0%,rgba(38,38,38,1)_100%)]">
                  <h3 className="font-montserrat font-semibold text-neutral-100 md:text-2xl text-center">
                    ESVC
                  </h3>
                </div>
                <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]">
                  <span className="font-montserrat font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">
                    Token-gated startup funding
                  </span>
                </div>
                <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]">
                  <span className="font-montserrat font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">
                    Backed by real assets
                  </span>
                </div>
                <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]">
                  <span className="font-montserrat font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">
                    Transparent treasury
                  </span>
                </div>
                <div className="md:flex items-center gap-3 px-5 py-2.5 w-full rounded-br-2xl border border-[#D19049]">
                  <span className="font-montserrat font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">
                    High-yield ESVC staking
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="">
            <div className="md:flex items-center gap-6">
              <button className="justify-center whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 shadow h-14 w-full px-4 py-2.5 bg-[#bf4129] hover:bg-[#a83a25] rounded-[999px] font-montserrat font-semibold text-neutral-50 text-lg flex items-center gap-3 mb-5 md:mb-0">
                Start Staking Now
                <Image
                  className="w-6 h-6"
                  alt="RocketIcon launch"
                  src="/c.animaapp.com/mc62dpc6QTKBF1/img/rocket-launch-24dp-00000-fill0-wght300-grad0-opsz24-1.svg"
                  width={24}
                  height={24}
                />
              </button>
              <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-background shadow-sm hover:bg-accent hover:text-accent-foreground w-full h-14 px-4 py-2.5 rounded-[999px] border border-solid border-neutral-700 font-montserrat font-semibold text-neutral-100 text-lg">
                See How it Works
              </button>
            </div>
          </div>
        </section>
      </div>
    </section>
  );
}
