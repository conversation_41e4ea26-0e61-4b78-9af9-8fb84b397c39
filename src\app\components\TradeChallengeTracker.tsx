import Image from "next/image";
import Link from "next/link";

export default function TradeChallengeTracker() {
  return (
    <section className="w-full p-10 bg-[#281705] font-montserrat">
      <div className="container max-w-[1200px] mx-auto relative">
        {/* Background blur effect */}
        <div className="absolute w-[239px] h-[239px] top-[73px] left-[41px] md:left-[481px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>
        
        <div className="flex flex-col items-center gap-4 mb-10 relative">
          <h2 className="text-[20px] lg:text-[32px] font-semibold text-center font-montserrat bg-gradient-to-r from-[#D19049] to-[#BF4129] bg-clip-text text-transparent">
            No hype. Just discipline, Bitcoin and a BOT.
          </h2>
          <p className="max-w-[670px] font-normal text-neutral-300 text-base text-center leading-6 font-montserrat">
            Watch Chief Gaius Chibueze take on the ultimate 1,000-trade compounding challenge. Tracked live and funded by ESVC Capital.
          </p>
          <Image
            className="hidden absolute w-[154px] h-[18px] top-[43px] left-[407px]"
            alt="Vector"
            src="/c.animaapp.com/mc62dpc6QTKBF1/img/vector-2.svg"
            width={154}
            height={18}
          />
        </div>
        
        <div className="md:flex items-center justify-center md:space-x-5 space-y-4">
          {/* Live Tracker Card */}
          <div className="bg-[#171717] p-4 rounded-2xl">
            <div className="font-semibold leading-none tracking-tight py-2">Live Tracker</div>
            <div className="rounded-xl border text-card-foreground shadow w-full lg:w-[350px] bg-transparent border-neutral-700">
              <div className="p-6 pt-0">
                {/* Trade Startup Capital */}
                <div className="flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden">
                  <div className="flex items-center gap-3 w-full">
                    <Image
                      className="w-11 h-11 rounded-4xl"
                      alt="TRADE STARTUP CAPITAL"
                      src="/c.animaapp.com/mc62dpc6QTKBF1/img/bitcoin-symbol-png.png"
                      width={44}
                      height={44}
                    />
                    <div className="flex flex-col items-start gap-3 flex-1">
                      <p className="self-stretch font-medium text-neutral-300 text-sm font-montserrat">
                        TRADE STARTUP CAPITAL
                      </p>
                      <p className="self-stretch font-semibold text-neutral-100 text-xl font-montserrat">
                        $91,000
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="shrink-0 h-[1px] w-full bg-neutral-700"></div>
                
                {/* Current Balance */}
                <div className="flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden">
                  <div className="flex items-center gap-3 w-full">
                    <Image
                      className="w-11 h-11 rounded-4xl"
                      alt="CURRENT BALANCE"
                      src="/c.animaapp.com/mc62dpc6QTKBF1/img/solana-icon-jpeg.png"
                      width={44}
                      height={44}
                    />
                    <div className="flex flex-col items-start gap-3 flex-1">
                      <p className="self-stretch font-medium text-neutral-300 text-sm font-montserrat">
                        CURRENT BALANCE
                      </p>
                      <p className="self-stretch font-semibold text-neutral-100 text-xl font-montserrat">
                        $124,000
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="shrink-0 h-[1px] w-full bg-neutral-700"></div>
                
                {/* Trade Count */}
                <div className="flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden">
                  <div className="flex items-center gap-3 w-full">
                    <Image
                      className="w-11 h-11 rounded-4xl"
                      alt="TRADE COUNT"
                      src="/c.animaapp.com/mc62dpc6QTKBF1/img/image.png"
                      width={44}
                      height={44}
                    />
                    <div className="flex flex-col items-start gap-3 flex-1">
                      <p className="self-stretch font-medium text-neutral-300 text-sm font-montserrat">
                        TRADE COUNT
                      </p>
                      <p className="self-stretch font-semibold text-neutral-100 text-xl font-montserrat">
                        $51,500
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="shrink-0 h-[1px] w-full bg-neutral-700"></div>
                
                {/* ROI So Far */}
                <div className="flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden">
                  <div className="flex items-center gap-3 w-full">
                    <Image
                      className="w-11 h-11 rounded-4xl"
                      alt="ROI SO FAR"
                      src="/c.animaapp.com/mc62dpc6QTKBF1/img/image-1.png"
                      width={44}
                      height={44}
                    />
                    <div className="flex flex-col items-start gap-3 flex-1">
                      <p className="self-stretch font-medium text-neutral-300 text-sm font-montserrat">
                        ROI SO FAR
                      </p>
                      <p className="self-stretch font-semibold text-neutral-100 text-xl font-montserrat">
                        +4.3%
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Live Balance Trend */}
          <div className="bg-[#171717] py-2 rounded-2xl">
            <h1 className="px-2">Live Balance Trend</h1>
            <div className="w-[300px] h-[250px] flex items-center justify-center text-neutral-500">
              {/* Placeholder for chart */}
              <div className="text-center">
                <div className="text-4xl mb-2">📈</div>
                <p>Chart Component</p>
                <p className="text-sm">Live trading data visualization</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Feature badges */}
        <div className="flex flex-col lg:flex-row items-center justify-center space-x-2 mt-8">
          <div className="flex justify-center items-center gap-x-2 my-3 lg:my-0 pr-2 w-fit border border-neutral-700 rounded-full">
            <div className="bg-[#183668] w-[52px] h-[52px] flex justify-center items-center rounded-full">
              <div className="bg-[#D8E6FD] w-[32px] h-[32px] flex justify-center items-center rounded-full text-black">✓</div>
            </div>
            Auto-trading logic
          </div>
          <div className="flex justify-center items-center gap-x-2 my-3 lg:my-0 pr-2 w-fit border border-neutral-700 rounded-full">
            <div className="bg-[#4F2E0B] w-[52px] h-[52px] flex justify-center items-center rounded-full">
              <div className="bg-[#D8E6FD] w-[32px] h-[32px] flex justify-center items-center rounded-full text-black">✓</div>
            </div>
            Custom rules by Gaius
          </div>
          <div className="flex justify-center items-center gap-x-2 my-3 lg:my-0 pr-2 w-fit border border-neutral-700 rounded-full">
            <div className="bg-[#7CCA8D] w-[52px] h-[52px] flex justify-center items-center rounded-full">
              <div className="bg-[#D8E6FD] w-[32px] h-[32px] flex justify-center items-center rounded-full text-black">✓</div>
            </div>
            Built for long-term compounding
          </div>
        </div>
        
        <Image
          className="absolute w-[39px] md:w-[78px] md:h-[52px] bottom-17 right-[-20px] md:bottom-[0px] md:right-[250px]"
          alt="Element"
          src="/c.animaapp.com/mc62dpc6QTKBF1/img/element-09.svg"
          width={78}
          height={52}
        />
        
        <div className="flex justify-center items-center w-full mt-8">
          <Link href="/trade-challenge">
            <button className="whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 shadow h-14 font-montserrat font-semibold text-[#171717] text-lg bg-[#C6741B] hover:bg-[#B8681A] rounded-full px-10 py-2.5 flex items-center justify-center gap-3">
              Get the Bot - $100/Year
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
}
