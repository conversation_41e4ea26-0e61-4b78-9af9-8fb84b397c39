import React, { useState } from 'react';
import '../../styles/components/user-dashboard/ResetPassword.css';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';

// Import icons
import lockIcon from '../../assets/wallet-money.png';
import tickCircleIcon from '../../assets/tick-circle.png';
import exclamationIcon from '../../assets/Exclamation _Icon.png';

type ResetStep = 'forgot-password' | 'verify-email' | 'set-new-password' | 'success';

const ResetPassword: React.FC = () => {
  const [activeTab, setActiveTab] = useState('security-settings');
  const [showBalances, setShowBalances] = useState(true);
  const [hideBalances, setHideBalances] = useState(false);
  const [currentStep, setCurrentStep] = useState<ResetStep>('forgot-password');
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showCodeError, setShowCodeError] = useState(false);

  const [passwordRequirements, setPasswordRequirements] = useState({
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecialChar: false,
    noCommonWords: false
  });

  // Toggle balance visibility
  const toggleBalances = () => {
    setShowBalances(!showBalances);
    setHideBalances(!hideBalances);
  };

  // Handle forgot password form submission
  const handleForgotPasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentStep('verify-email');
  };

  // Handle verification code input
  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      setVerificationCode(newCode);
      setShowCodeError(false);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  // Handle verification code submission
  const handleVerifyCode = () => {
    const code = verificationCode.join('');
    if (code.length === 6) {
      // Simulate wrong code for demo
      if (code === '123456') {
        setCurrentStep('set-new-password');
      } else {
        setShowCodeError(true);
      }
    }
  };

  // Handle new password validation
  const handleNewPasswordChange = (value: string) => {
    setNewPassword(value);
    setPasswordRequirements({
      minLength: value.length >= 8,
      hasUppercase: /[A-Z]/.test(value),
      hasLowercase: /[a-z]/.test(value),
      hasNumber: /\d/.test(value),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(value),
      noCommonWords: !/(password|123456|qwerty)/i.test(value)
    });
  };

  // Handle new password form submission
  const handleNewPasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (newPassword !== confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    const allRequirementsMet = Object.values(passwordRequirements).every(req => req);
    if (!allRequirementsMet) {
      alert('Please ensure all password requirements are met');
      return;
    }

    setCurrentStep('success');
  };

  const handleGoBack = () => {
    window.history.back();
  };

  const handleProceedToLogin = () => {
    // Navigate to login page
    window.location.href = '/login';
  };

  const handleChangeEmailAddress = () => {
    setCurrentStep('forgot-password');
    setVerificationCode(['', '', '', '', '', '']);
    setShowCodeError(false);
  };

  return (
    <UserDashboardLayout className="reset-password-container">
      <div className="reset-password-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosín 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>
          </div>
          
          <div className="header-controls">
            <button className="stake-esvc-btn">
              <img src={lockIcon} alt="Stake" className="btn-icon" />
              Stake ESVC
            </button>
            
            <div className="balance-toggle">
              <span className="toggle-label">Hide balances</span>
              <label className="toggle-switch">
                <input 
                  type="checkbox" 
                  checked={hideBalances}
                  onChange={toggleBalances}
                />
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* Reset Password Content */}
        <div className="reset-password-main">
          <div className="reset-header">
            <button className="back-btn" onClick={handleGoBack}>
              ← Reset Password
            </button>
          </div>

          <div className="reset-form-container">
            {currentStep === 'forgot-password' && (
              <div className="forgot-password-form">
                <h2 className="form-title">Forgot Your Password?</h2>
                <p className="form-description">
                  We'll send you a link to reset your password.
                </p>
                
                <form onSubmit={handleForgotPasswordSubmit}>
                  <div className="form-group">
                    <label className="form-label">Email Address</label>
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="form-input"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  
                  <button type="submit" className="reset-btn">
                    Send Verification Code
                  </button>
                </form>
              </div>
            )}

            {currentStep === 'verify-email' && (
              <div className="verify-email-form">
                <h2 className="form-title">Verify Your Email</h2>
                <p className="form-description">
                  Enter the 6-digit code sent to your email address
                  <br />
                  <strong>{email}</strong>
                </p>
                
                <div className="verification-code">
                  {verificationCode.map((digit, index) => (
                    <input
                      key={index}
                      id={`code-${index}`}
                      type="text"
                      value={digit}
                      onChange={(e) => handleCodeChange(index, e.target.value)}
                      className={`code-input ${showCodeError ? 'error' : ''}`}
                      maxLength={1}
                    />
                  ))}
                </div>

                {showCodeError && (
                  <div className="error-message">
                    <img src={exclamationIcon} alt="Error" className="error-icon" />
                    <span>Invalid verification code. Please try again.</span>
                  </div>
                )}
                
                <button className="verify-btn" onClick={handleVerifyCode}>
                  Verify Code
                </button>

                <p className="resend-text">
                  Didn't receive the code? <button className="link-btn">Resend</button>
                </p>

                <button className="change-email-btn" onClick={handleChangeEmailAddress}>
                  ← Change Email Address
                </button>
              </div>
            )}

            {currentStep === 'set-new-password' && (
              <div className="new-password-form">
                <h2 className="form-title">Set a New Password</h2>
                <p className="form-description">
                  Create a strong password for your account.
                </p>
                
                <form onSubmit={handleNewPasswordSubmit}>
                  <div className="form-group">
                    <label className="form-label">New Password</label>
                    <input
                      type="password"
                      value={newPassword}
                      onChange={(e) => handleNewPasswordChange(e.target.value)}
                      className="form-input"
                      placeholder="••••••••"
                      required
                    />
                  </div>

                  <div className="password-requirements">
                    <p className="requirements-title">Password must contain:</p>
                    <ul className="requirements-list">
                      <li className={passwordRequirements.minLength ? 'requirement-met' : 'requirement-unmet'}>
                        At least 8 characters
                      </li>
                      <li className={passwordRequirements.hasUppercase ? 'requirement-met' : 'requirement-unmet'}>
                        At least one uppercase letter
                      </li>
                      <li className={passwordRequirements.hasLowercase ? 'requirement-met' : 'requirement-unmet'}>
                        At least one lowercase letter
                      </li>
                      <li className={passwordRequirements.hasNumber ? 'requirement-met' : 'requirement-unmet'}>
                        At least one number
                      </li>
                      <li className={passwordRequirements.hasSpecialChar ? 'requirement-met' : 'requirement-unmet'}>
                        At least one special character (!@#$%^&*)
                      </li>
                      <li className={passwordRequirements.noCommonWords ? 'requirement-met' : 'requirement-unmet'}>
                        Cannot be a common password
                      </li>
                    </ul>
                  </div>

                  <div className="form-group">
                    <label className="form-label">Re-Enter New Password</label>
                    <input
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="form-input"
                      placeholder="••••••••"
                      required
                    />
                  </div>
                  
                  <button type="submit" className="reset-btn">
                    Reset Password
                  </button>
                </form>
              </div>
            )}

            {currentStep === 'success' && (
              <div className="success-form">
                <div className="success-content">
                  <img src={tickCircleIcon} alt="Success" className="success-icon" />
                  <h2 className="success-title">Your password has been reset!</h2>
                  <p className="success-message">
                    You can now log in with your new password.
                  </p>
                  <button className="login-btn" onClick={handleProceedToLogin}>
                    Proceed to Login
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* User Side Navigation */}
      <UserSideNav 
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
    </UserDashboardLayout>
  );
};

export default ResetPassword;
