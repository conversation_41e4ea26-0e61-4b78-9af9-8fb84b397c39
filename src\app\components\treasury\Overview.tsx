'use client';

import React from 'react';

const Overview: React.FC = () => {
  // Dashboard cards data matching the legacy design
  const dashboardCards = [
    {
      title: 'TOTAL AMOUNT OF ESVC STAKED',
      value: '177,874,389.00',
      unit: 'ESVC',
      change: '****% Today',
      changeType: 'positive'
    },
    {
      title: 'FUNDS AVAILABLE FOR DAILY ISO PAYOUTS',
      value: '$609,185',
      change: '+7.8% Today',
      changeType: 'positive'
    },
    {
      title: 'FUNDS AVAILABLE TO FUND STARTUPS',
      value: '$43,700',
      change: '+4.8% Today',
      changeType: 'positive',
      hasInfoIcon: true
    },
    {
      title: 'TOTAL NUMBER OF STAKERS',
      value: '11,302',
      change: '+3.8% Today',
      changeType: 'positive'
    },
    {
      title: 'TOTAL ESVC SOLD TO DATE',
      value: '$29,400,200.00',
      change: '****% Today',
      changeType: 'positive'
    },
    {
      title: 'TOTAL PROFIT GENERATED',
      value: '$43,700',
      change: '****% Today',
      changeType: 'positive'
    }
  ];

  return (
    <div className="flex-1 max-w-[920px]">
      {/* Section Header */}
      <div className="mb-4">
        <h2 className="text-3xl font-semibold text-white m-0 font-montserrat">Overview</h2>
      </div>

      {/* Dashboard Cards Grid - Desktop: 3 cols, Tablet: 2 cols, Mobile: 1 col */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mx-auto mb-12 max-w-fit items-start justify-center">
        {dashboardCards.map((card, index) => (
          <div
            key={index}
            className="bg-[rgba(38,38,38,0.8)] backdrop-blur-[20px] border border-[rgba(255,255,255,0.1)] rounded-xl p-4 w-full min-h-[120px] flex flex-col gap-3 transition-all duration-300 shadow-[0_8px_32px_rgba(0,0,0,0.2)] text-center hover:transform hover:-translate-y-1 hover:shadow-[0_12px_40px_rgba(0,0,0,0.3)] hover:border-[rgba(191,65,41,0.3)]

            /* Mobile specific styles */
            max-md:w-[105%] max-md:max-w-none max-md:py-6 max-md:px-4 max-md:min-h-[120px] max-md:text-left max-md:-ml-[5%] max-md:-mr-[5%]"
          >
            <div className="flex items-start justify-between mb-2 max-md:flex-col max-md:items-start max-md:gap-1 max-md:mb-3">
              <h3 className="font-montserrat text-xs font-semibold text-[#999999] uppercase tracking-wide m-0 mb-2 leading-4 flex items-center justify-center gap-2 max-md:justify-start max-md:mt-2 max-md:mb-3 max-md:font-medium">
                {card.title}
                {card.hasInfoIcon && (
                  <span className="w-4 h-4">ℹ️</span>
                )}
              </h3>
            </div>
            <div className="flex flex-col gap-2">
              <div className="font-montserrat text-2xl font-bold text-white flex items-baseline justify-center gap-2 max-md:justify-start max-md:mb-1 max-md:leading-tight">
                {card.value}
                {card.unit && <span className="text-lg font-medium text-[#CCCCCC] max-md:text-sm max-md:opacity-80 max-md:ml-1">{card.unit}</span>}
              </div>
              <div className={`text-base font-medium flex items-center justify-center gap-2 max-md:justify-start max-md:text-sm max-md:mt-1 max-md:gap-1 ${
                card.changeType === 'positive' ? 'text-[#4CAF50]' : 'text-[#FF6B6B]'
              }`}>
                <span className="w-4 h-4">📈</span>
                {card.change}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Overview;
