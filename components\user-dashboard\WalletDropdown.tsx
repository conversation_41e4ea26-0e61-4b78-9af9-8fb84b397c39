import React, { useState, useRef, useEffect } from 'react';
import '../../styles/components/user-dashboard/WalletDropdown.css';

// Import icons
import chevronDownIcon from '../../assets/arrow-down.png';
import walletIcon from '../../assets/wallet-money.png';

interface WalletOption {
  id: string;
  label: string;
  amount: string;
  icon: string;
}

interface WalletDropdownProps {
  options: WalletOption[];
  selectedValue?: string;
  onSelect: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const WalletDropdown: React.FC<WalletDropdownProps> = ({
  options,
  selectedValue,
  onSelect,
  placeholder = "Select a wallet",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<WalletOption | null>(
    options.find(option => option.id === selectedValue) || null
  );
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle option selection
  const handleOptionSelect = (option: WalletOption) => {
    setSelectedOption(option);
    setIsOpen(false);
    onSelect(option.id);
  };

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleDropdown();
    } else if (event.key === 'Escape') {
      setIsOpen(false);
    }
  };

  return (
    <div 
      className={`wallet-dropdown-container ${className} ${isOpen ? 'open' : ''}`}
      ref={dropdownRef}
    >
      {/* Dropdown Trigger */}
      <button
        className="wallet-dropdown-trigger"
        onClick={toggleDropdown}
        onKeyDown={handleKeyDown}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        type="button"
      >
        <div className="trigger-content">
          <div className="trigger-icon">
            <img src={selectedOption?.icon || walletIcon} alt="Wallet" />
          </div>
          <div className="trigger-text">
            {selectedOption ? (
              <>
                <span className="wallet-label">{selectedOption.label}</span>
                <span className="wallet-amount">{selectedOption.amount}</span>
              </>
            ) : (
              <span className="wallet-placeholder">{placeholder}</span>
            )}
          </div>
        </div>
        <div className={`dropdown-arrow ${isOpen ? 'open' : ''}`}>
          <img src={chevronDownIcon} alt="Toggle" />
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="wallet-dropdown-menu" role="listbox">
          {options.map((option) => (
            <button
              key={option.id}
              className={`wallet-dropdown-option ${selectedOption?.id === option.id ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(option)}
              role="option"
              aria-selected={selectedOption?.id === option.id}
              type="button"
            >
              <div className="option-icon">
                <img src={option.icon} alt="Wallet" />
              </div>
              <div className="option-content">
                <span className="option-label">{option.label}</span>
                <span className="option-amount">{option.amount}</span>
              </div>
              {selectedOption?.id === option.id && (
                <div className="option-check">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path 
                      d="M13.5 4.5L6 12L2.5 8.5" 
                      stroke="currentColor" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default WalletDropdown;
