import Image from "next/image";

export default function TreasuryDashboard() {
  return (
    <section className="w-full p-10 bg-neutral-800">
      <div className="container max-w-[1200px] mx-auto relative">
        {/* Background blur effect */}
        <div className="absolute w-[239px] h-[239px] top-[73px] lg:left-[41px] md:left-[481px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>
        
        <div className="flex flex-col items-center gap-4 mb-10 relative">
          <h2 className="text-[40px] font-semibold text-neutral-100 text-center font-montserrat">Treasury Dashboard</h2>
          <p className="max-w-[670px] font-normal text-neutral-300 text-base text-center leading-6 font-montserrat">
            Live insights into how funds are held, RO<PERSON> is distributed, and startups are supported. Empowering you to stake with trust.
          </p>
          <Image
            className="hidden absolute w-[154px] h-[18px] top-[43px] left-[407px]"
            alt="Vector"
            src="/c.animaapp.com/mc62dpc6QTKBF1/img/vector-2.svg"
            width={154}
            height={18}
          />
        </div>
        
        <div className="md:flex md:flex-col w-full gap-6">
          {/* Top row cards */}
          <div className="md:flex items-center gap-6 w-full">
            {/* Total Capital Invested */}
            <div className="rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4">
              <div className="flex flex-col items-start gap-4 p-4">
                <div className="flex items-center gap-2 w-full mt-[-1.00px]">
                  <p className="font-medium text-neutral-300 text-sm font-montserrat">Total Capital Invested</p>
                </div>
                <p className="self-stretch font-semibold text-neutral-100 text-[28px] font-montserrat">$266,500</p>
                <div className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trending-up w-4 h-4 text-[#7cca8d]" aria-hidden="true">
                    <path d="M16 7h6v6"></path>
                    <path d="m22 7-8.5 8.5-5-5L2 17"></path>
                  </svg>
                  <span className="font-normal text-xs font-montserrat text-[#7cca8d]">+ 4.8% Today</span>
                </div>
              </div>
            </div>
            
            {/* Total Profit Generated */}
            <div className="rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4">
              <div className="flex flex-col items-start gap-4 p-4">
                <div className="flex items-center gap-2 w-full mt-[-1.00px]">
                  <p className="font-medium text-neutral-300 text-sm font-montserrat">Total Profit Generated</p>
                </div>
                <p className="self-stretch font-semibold text-neutral-100 text-[28px] font-montserrat">$43,700</p>
                <div className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trending-up w-4 h-4 text-[#7cca8d]" aria-hidden="true">
                    <path d="M16 7h6v6"></path>
                    <path d="m22 7-8.5 8.5-5-5L2 17"></path>
                  </svg>
                  <span className="font-normal text-xs font-montserrat text-[#7cca8d]">+ 4.8% Today</span>
                </div>
              </div>
            </div>
            
            {/* Funds Available */}
            <div className="rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4">
              <div className="flex flex-col items-start gap-4 p-4">
                <div className="flex items-center gap-2 w-full mt-[-1.00px]">
                  <p className="font-medium text-neutral-300 text-sm font-montserrat">Funds Available to Fund Startups</p>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-info w-5 h-5 text-neutral-300" aria-hidden="true">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M12 16v-4"></path>
                    <path d="M12 8h.01"></path>
                  </svg>
                </div>
                <p className="self-stretch font-semibold text-neutral-100 text-[28px] font-montserrat">$2,185</p>
                <div className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trending-up w-4 h-4 text-[#7cca8d]" aria-hidden="true">
                    <path d="M16 7h6v6"></path>
                    <path d="m22 7-8.5 8.5-5-5L2 17"></path>
                  </svg>
                  <span className="font-normal text-xs font-montserrat text-[#7cca8d]">+ 4.8% Today</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Holdings section */}
          <div className="rounded-xl border text-card-foreground shadow w-full bg-transparent border-neutral-700">
            <div className="p-6 pt-0 md:flex md:items-start md:gap-4">
              {/* BTC Holdings */}
              <div className="flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden">
                <div className="flex items-center gap-3 w-full">
                  <Image
                    className="w-11 h-11 rounded-4xl"
                    alt="BTC HOLDINGS"
                    src="/c.animaapp.com/mc62dpc6QTKBF1/img/bitcoin-symbol-png.png"
                    width={44}
                    height={44}
                  />
                  <div className="flex flex-col items-start gap-3 flex-1">
                    <p className="self-stretch font-medium text-neutral-300 text-sm font-montserrat">BTC HOLDINGS</p>
                    <p className="self-stretch font-semibold text-neutral-100 text-xl font-montserrat">
                      <span>$91,000 </span>
                      <span className="text-sm">BTC</span>
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trending-up w-4 h-4 text-[#7cca8d]" aria-hidden="true">
                    <path d="M16 7h6v6"></path>
                    <path d="m22 7-8.5 8.5-5-5L2 17"></path>
                  </svg>
                  <span className="font-normal text-xs font-montserrat text-[#7cca8d]">+ 4.8% Today</span>
                </div>
              </div>
              
              <div>
                <Image
                  className="self-stretch object-cover py-2 hidden md:block"
                  alt="Line"
                  src="/c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg"
                  width={1}
                  height={100}
                />
                <div data-orientation="horizontal" role="none" className="shrink-0 h-[1px] w-full bg-neutral-700"></div>
              </div>
              
              {/* SOL Holdings */}
              <div className="flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden">
                <div className="flex items-center gap-3 w-full">
                  <Image
                    className="w-11 h-11 rounded-4xl"
                    alt="SOL HOLDINGS"
                    src="/c.animaapp.com/mc62dpc6QTKBF1/img/solana-icon-jpeg.png"
                    width={44}
                    height={44}
                  />
                  <div className="flex flex-col items-start gap-3 flex-1">
                    <p className="self-stretch font-medium text-neutral-300 text-sm font-montserrat">SOL HOLDINGS</p>
                    <p className="self-stretch font-semibold text-neutral-100 text-xl font-montserrat">
                      <span>$175,500 </span>
                      <span className="text-sm">SOL</span>
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Image
                    className="w-4 h-4"
                    alt="Trend icon"
                    src="/c.animaapp.com/mc62dpc6QTKBF1/img/trend-icon-1.svg"
                    width={16}
                    height={16}
                  />
                  <span className="font-normal text-xs font-montserrat text-[#7cca8d]">+ 4.8% Today</span>
                </div>
              </div>

              <div>
                <Image
                  className="self-stretch object-cover py-2 hidden md:block"
                  alt="Line"
                  src="/c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg"
                  width={1}
                  height={100}
                />
                <div data-orientation="horizontal" role="none" className="shrink-0 h-[1px] w-full bg-neutral-700"></div>
              </div>

              {/* USDC Holdings */}
              <div className="flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden">
                <div className="flex items-center gap-3 w-full">
                  <Image
                    className="w-11 h-11 rounded-4xl"
                    alt="USDC HOLDINGS"
                    src="/c.animaapp.com/mc62dpc6QTKBF1/img/image.png"
                    width={44}
                    height={44}
                  />
                  <div className="flex flex-col items-start gap-3 flex-1">
                    <p className="self-stretch font-medium text-neutral-300 text-sm font-montserrat">USDC HOLDINGS</p>
                    <p className="self-stretch font-semibold text-neutral-100 text-xl font-montserrat">
                      <span>$0 </span>
                      <span className="text-sm">USDC</span>
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Image
                    className="w-4 h-4"
                    alt="Trend icon"
                    src="/c.animaapp.com/mc62dpc6QTKBF1/img/trend-icon-1.svg"
                    width={16}
                    height={16}
                  />
                  <span className="font-normal text-xs font-montserrat text-[#7cca8d]">+ 4.8% Today</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <Image
          className="absolute w-[39px] md:w-[78px] md:h-[52px] bottom-17 right-[-20px] md:bottom-[0px] md:right-[250px]"
          alt="Element"
          src="/c.animaapp.com/mc62dpc6QTKBF1/img/element-09.svg"
          width={78}
          height={52}
        />
        
        <div className="flex justify-center items-center w-full">
          <div className="md:flex md:items-center md:justify-center md:gap-6 mt-10">
            <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 text-primary-foreground shadow mb-5 md:mb-0 h-14 w-full px-4 py-2.5 bg-[#bf4129] hover:bg-[#a83a25] rounded-[999px] text-lg font-semibold font-montserrat">
              Start Staking Now
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-rocket w-6 h-6 ml-3" aria-hidden="true">
                <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"></path>
                <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"></path>
                <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></path>
                <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path>
              </svg>
            </button>
            <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-14 w-full px-4 py-2.5 rounded-[999px] border-neutral-700 text-neutral-100 text-lg font-semibold font-montserrat hover:border-neutral-500">
              View Full Breakdown
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-right w-6 h-6 ml-3" aria-hidden="true">
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
