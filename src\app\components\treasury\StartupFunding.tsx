'use client';

import React from 'react';

const StartupFunding: React.FC = () => {
  const fundedStartups = [
    {
      id: 1,
      name: 'TechFlow AI',
      category: 'Artificial Intelligence',
      fundingAmount: '$50,000',
      fundingDate: '2024-01-15',
      status: 'Active',
      description: 'AI-powered workflow automation for small businesses',
      founder: '<PERSON>',
      progress: 75,
      milestones: ['MVP Completed', 'First 100 Users', 'Revenue Positive']
    },
    {
      id: 2,
      name: 'GreenEnergy Solutions',
      category: 'Clean Tech',
      fundingAmount: '$75,000',
      fundingDate: '2024-01-08',
      status: 'Active',
      description: 'Solar panel optimization using IoT sensors',
      founder: '<PERSON>',
      progress: 60,
      milestones: ['Prototype Built', 'Pilot Program', 'Series A Prep']
    },
    {
      id: 3,
      name: 'HealthTrack Pro',
      category: 'Healthcare',
      fundingAmount: '$40,000',
      fundingDate: '2023-12-20',
      status: 'Completed',
      description: 'Wearable health monitoring for elderly care',
      founder: 'Dr. <PERSON>',
      progress: 100,
      milestones: ['FDA Approval', 'Market Launch', 'Exit Strategy']
    }
  ];

  const pendingApplications = [
    {
      id: 1,
      name: 'CryptoSecure Wallet',
      category: 'Blockchain',
      requestedAmount: '$60,000',
      submissionDate: '2024-01-20',
      founder: 'Alex Kim',
      description: 'Hardware wallet with biometric security'
    },
    {
      id: 2,
      name: 'EduLearn Platform',
      category: 'Education',
      requestedAmount: '$45,000',
      submissionDate: '2024-01-18',
      founder: 'Maria Santos',
      description: 'Personalized learning platform for K-12'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Funding Overview */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Startup Funding Overview</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Total Funded</h3>
            <p className="text-white text-3xl font-bold font-montserrat">$480K</p>
            <p className="text-green-400 text-sm font-montserrat mt-2">12 startups</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Active Projects</h3>
            <p className="text-white text-3xl font-bold font-montserrat">8</p>
            <p className="text-blue-400 text-sm font-montserrat mt-2">In progress</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Success Rate</h3>
            <p className="text-white text-3xl font-bold font-montserrat">75%</p>
            <p className="text-purple-400 text-sm font-montserrat mt-2">ROI positive</p>
          </div>
          
          <div className="bg-neutral-800 rounded-xl p-6 text-center">
            <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Pending Applications</h3>
            <p className="text-white text-3xl font-bold font-montserrat">5</p>
            <p className="text-yellow-400 text-sm font-montserrat mt-2">Under review</p>
          </div>
        </div>
      </div>

      {/* Funded Startups */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Funded Startups</h2>
        
        <div className="space-y-6">
          {fundedStartups.map((startup) => (
            <div key={startup.id} className="bg-neutral-800 rounded-xl p-6">
              <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-3">
                    <h3 className="text-white text-xl font-bold font-montserrat">{startup.name}</h3>
                    <div className={`px-3 py-1 rounded-full text-xs font-montserrat ${
                      startup.status === 'Active' 
                        ? 'bg-green-500/20 text-green-400' 
                        : 'bg-blue-500/20 text-blue-400'
                    }`}>
                      {startup.status}
                    </div>
                  </div>
                  
                  <p className="text-neutral-300 font-montserrat mb-3">{startup.description}</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-neutral-400 font-montserrat">Category:</span>
                      <span className="text-white font-montserrat ml-2">{startup.category}</span>
                    </div>
                    <div>
                      <span className="text-neutral-400 font-montserrat">Founder:</span>
                      <span className="text-white font-montserrat ml-2">{startup.founder}</span>
                    </div>
                    <div>
                      <span className="text-neutral-400 font-montserrat">Funded:</span>
                      <span className="text-white font-montserrat ml-2">{startup.fundingDate}</span>
                    </div>
                  </div>
                </div>
                
                <div className="lg:w-80">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-neutral-400 text-sm font-montserrat">Progress</span>
                    <span className="text-white font-montserrat font-semibold">{startup.progress}%</span>
                  </div>
                  <div className="w-full bg-neutral-700 rounded-full h-2 mb-4">
                    <div 
                      className="bg-[#BF4129] h-2 rounded-full" 
                      style={{ width: `${startup.progress}%` }}
                    ></div>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-[#BF4129] text-2xl font-bold font-montserrat">{startup.fundingAmount}</p>
                    <button className="text-[#BF4129] hover:text-[#a83a25] text-sm font-montserrat font-medium transition-colors duration-300 mt-1">
                      View Details →
                    </button>
                  </div>
                </div>
              </div>
              
              {/* Milestones */}
              <div className="mt-6 pt-6 border-t border-neutral-700">
                <h4 className="text-white font-montserrat font-semibold mb-3">Key Milestones</h4>
                <div className="flex flex-wrap gap-2">
                  {startup.milestones.map((milestone, index) => (
                    <div key={index} className="bg-neutral-700 px-3 py-1 rounded-full">
                      <span className="text-neutral-300 text-sm font-montserrat">{milestone}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pending Applications */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Pending Applications</h2>
        
        <div className="space-y-4">
          {pendingApplications.map((application) => (
            <div key={application.id} className="bg-neutral-800 rounded-xl p-6">
              <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-2">
                    <h3 className="text-white text-lg font-bold font-montserrat">{application.name}</h3>
                    <div className="px-3 py-1 rounded-full text-xs font-montserrat bg-yellow-500/20 text-yellow-400">
                      Under Review
                    </div>
                  </div>
                  
                  <p className="text-neutral-300 font-montserrat mb-3">{application.description}</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-neutral-400 font-montserrat">Category:</span>
                      <span className="text-white font-montserrat ml-2">{application.category}</span>
                    </div>
                    <div>
                      <span className="text-neutral-400 font-montserrat">Founder:</span>
                      <span className="text-white font-montserrat ml-2">{application.founder}</span>
                    </div>
                    <div>
                      <span className="text-neutral-400 font-montserrat">Submitted:</span>
                      <span className="text-white font-montserrat ml-2">{application.submissionDate}</span>
                    </div>
                  </div>
                </div>
                
                <div className="lg:w-60 text-right">
                  <p className="text-yellow-400 text-xl font-bold font-montserrat mb-2">{application.requestedAmount}</p>
                  <div className="flex gap-2 justify-end">
                    <button className="h-10 bg-green-600 hover:bg-green-700 border-none rounded-lg text-white font-montserrat text-sm font-semibold cursor-pointer transition-all duration-300 px-4">
                      Approve
                    </button>
                    <button className="h-10 bg-red-600 hover:bg-red-700 border-none rounded-lg text-white font-montserrat text-sm font-semibold cursor-pointer transition-all duration-300 px-4">
                      Reject
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Funding Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Funding by Category</h3>
          
          <div className="space-y-4">
            {[
              { category: 'AI/ML', amount: '$180K', percentage: 37.5, color: 'bg-blue-500' },
              { category: 'Clean Tech', amount: '$120K', percentage: 25, color: 'bg-green-500' },
              { category: 'Healthcare', amount: '$90K', percentage: 18.75, color: 'bg-red-500' },
              { category: 'Blockchain', amount: '$60K', percentage: 12.5, color: 'bg-purple-500' },
              { category: 'Education', amount: '$30K', percentage: 6.25, color: 'bg-yellow-500' }
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                  <span className="text-neutral-300 font-montserrat text-sm">{item.category}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-24 bg-neutral-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${item.color}`} 
                      style={{ width: `${item.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-white font-montserrat font-semibold text-sm w-16 text-right">
                    {item.amount}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Funding Timeline</h3>
          
          <div className="space-y-4">
            {[
              { month: 'Jan 2024', amount: '$165K', startups: 3 },
              { month: 'Dec 2023', amount: '$120K', startups: 2 },
              { month: 'Nov 2023', amount: '$95K', startups: 2 },
              { month: 'Oct 2023', amount: '$100K', startups: 3 }
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-neutral-300 font-montserrat text-sm">{item.month}</span>
                <div className="flex items-center gap-4">
                  <span className="text-neutral-400 font-montserrat text-sm">{item.startups} startups</span>
                  <span className="text-white font-montserrat font-semibold">{item.amount}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StartupFunding;
