'use client';

import React, { useState } from 'react';
import Overview from './Overview';
import LiveReserve from './LiveReserve';
import DailyTransactions from './DailyTransactions';
import RealTimeStaking from './RealTimeStaking';
import StartupFunding from './StartupFunding';
import ROIDistribution from './ROIDistribution';
import VisualAnalytics from './VisualAnalytics';
import TreasurySideNav from './TreasurySideNav';

type TreasuryTab = 'overview' | 'live-reserve' | 'daily-transactions' | 'real-time-staking' | 'startup-funding' | 'roi-distribution' | 'visual-analytics';

const TreasuryDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TreasuryTab>('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', component: Overview },
    { id: 'live-reserve', label: 'Live Reserve', component: LiveReserve },
    { id: 'daily-transactions', label: 'Daily Transactions', component: DailyTransactions },
    { id: 'real-time-staking', label: 'Real Time Staking', component: RealTimeStaking },
    { id: 'startup-funding', label: 'Startup Funding', component: StartupFunding },
    { id: 'roi-distribution', label: 'ROI Distribution', component: ROIDistribution },
    { id: 'visual-analytics', label: 'Visual Analytics', component: VisualAnalytics },
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || Overview;

  return (
    <div className="min-h-screen bg-[#171717] relative">
      {/* Background overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/20 pointer-events-none"></div>

      <div className="relative z-10">
        {/* Page Title */}
        <div className="text-center py-15 px-10 max-w-[800px] mx-auto max-md:py-5 max-md:px-5">
          <h1 className="text-6xl font-semibold mb-6 text-white flex flex-row items-start justify-center gap-5 font-montserrat max-md:text-3xl max-md:gap-2 max-md:mb-3">
            <div className="flex flex-col items-center gap-0.5">
              Treasury
              <div className="max-w-[300px] h-auto w-72 h-1 bg-gradient-to-r from-[#D19049] to-[#BF4129] rounded-full max-md:max-w-[120px]"></div>
            </div>
            Dashboard
          </h1>
          <p className="text-lg text-[#CCCCCC] leading-7 m-0 max-md:text-xs max-md:leading-[18px] max-md:-mb-2.5">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        <div className="flex gap-10 px-10 max-w-[1400px] mx-auto max-md:flex-col max-md:px-5 max-md:gap-4">
          {/* Dashboard Content Wrapper */}
          <div className="flex gap-10 w-full max-md:flex-col max-md:gap-3">
            {/* Sidebar */}
            <TreasurySideNav
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Main Content */}
            <ActiveComponent />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TreasuryDashboard;
