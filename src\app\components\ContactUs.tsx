'use client';

import React, { useState } from 'react';

const ContactUs: React.FC = () => {
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    emailAddress: '',
    subject: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Show success modal
    setShowSuccessModal(true);
  };

  const handleCloseModal = () => {
    setShowSuccessModal(false);
    // Reset form
    setFormData({
      fullName: '',
      emailAddress: '',
      subject: '',
      message: ''
    });
  };

  return (
    <div className="w-full py-20">
      <div className="max-w-7xl mx-auto px-6">
        {/* Page Header */}
        <div className="text-center mb-16">
          <h1 className="text-white text-5xl font-bold font-montserrat mb-4 flex items-center justify-center gap-4">
            Contact
            <div className="w-24 h-1 bg-gradient-to-r from-[#D19049] to-[#BF4129] rounded-full"></div>
            Us
          </h1>
          <p className="text-neutral-300 text-lg font-montserrat max-w-2xl mx-auto leading-relaxed">
            Have questions about staking, startup pitching, or our platform?
            Reach out to our support team. We're happy to assist you.
          </p>
        </div>

        {/* Contact Section */}
        <div className="max-w-6xl mx-auto">
          {/* Contact Header */}
          <div className="text-center mb-12">
            <h2 className="text-white text-4xl font-bold font-montserrat mb-4">We're Here to Help</h2>
            <p className="text-neutral-300 text-lg font-montserrat max-w-2xl mx-auto leading-relaxed">
              Have questions about staking, startup pitching, or our platform?
              Reach out to our support team. We're happy to assist you.
            </p>
          </div>

          {/* Stay Safe Notice */}
          <div className="flex items-center gap-4 bg-yellow-500/10 border border-yellow-500/30 rounded-xl p-4 mb-12">
            <div className="text-2xl flex-shrink-0">🛡️</div>
            <div className="flex flex-col gap-1">
              <span className="text-yellow-400 font-semibold font-montserrat">Stay Safe — Protect Your Wallet</span>
              <span className="text-neutral-300 text-sm font-montserrat">All messages are handled securely. We'll never ask for your wallet backup passwords.</span>
            </div>
          </div>

          {/* Contact Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
            {/* General Inquiries */}
            <div className="bg-[#262626] rounded-2xl p-6">
              <h3 className="text-white text-xl font-semibold font-montserrat mb-5">General Inquiries</h3>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="text-xl mt-0.5">📧</div>
                  <div className="flex flex-col gap-1">
                    <span className="text-white text-sm font-semibold font-montserrat">Email</span>
                    <span className="text-neutral-300 text-sm font-montserrat"><EMAIL></span>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="text-xl mt-0.5">📞</div>
                  <div className="flex flex-col gap-1">
                    <span className="text-white text-sm font-semibold font-montserrat">Response Time</span>
                    <span className="text-neutral-300 text-sm font-montserrat">Usually within 24 hours</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Startup Pitch Support */}
            <div className="bg-[#262626] rounded-2xl p-6">
              <h3 className="text-white text-xl font-semibold font-montserrat mb-5">Startup Pitch Support</h3>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="text-xl mt-0.5">📧</div>
                  <div className="flex flex-col gap-1">
                    <span className="text-white text-sm font-semibold font-montserrat">Email</span>
                    <span className="text-neutral-300 text-sm font-montserrat"><EMAIL></span>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="text-xl mt-0.5">💡</div>
                  <div className="flex flex-col gap-1">
                    <span className="text-neutral-300 text-sm font-montserrat">Questions about pitch eligibility, submissions, or feedback</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Media & Partnerships */}
            <div className="bg-[#262626] rounded-2xl p-6">
              <h3 className="text-white text-xl font-semibold font-montserrat mb-5">Media & Partnerships</h3>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="text-xl mt-0.5">📧</div>
                  <div className="flex flex-col gap-1">
                    <span className="text-white text-sm font-semibold font-montserrat">Email</span>
                    <span className="text-neutral-300 text-sm font-montserrat"><EMAIL></span>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="text-xl mt-0.5">🤝</div>
                  <div className="flex flex-col gap-1">
                    <span className="text-neutral-300 text-sm font-montserrat">For partnerships, PR or collaborations</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-[#262626] rounded-2xl p-8">
            <h3 className="text-white text-2xl font-semibold font-montserrat mb-2">Contact Form</h3>
            <p className="text-neutral-300 font-montserrat mb-8">Send us a quick message and we will respond promptly.</p>

            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="block text-white text-sm font-semibold font-montserrat">Full Name</label>
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] placeholder-neutral-500"
                    placeholder="Enter your full name..."
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-white text-sm font-semibold font-montserrat">Email Address</label>
                  <input
                    type="email"
                    name="emailAddress"
                    value={formData.emailAddress}
                    onChange={handleInputChange}
                    className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] placeholder-neutral-500"
                    placeholder="Enter your email address..."
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-white text-sm font-semibold font-montserrat">Subject</label>
                <input
                  type="text"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  className="w-full h-12 bg-neutral-800 border border-neutral-700 rounded-full px-5 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] placeholder-neutral-500"
                  placeholder="What is this about..."
                  required
                />
              </div>

              <div className="space-y-2">
                <label className="block text-white text-sm font-semibold font-montserrat">Message</label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  className="w-full bg-neutral-800 border border-neutral-700 rounded-xl px-5 py-4 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:ring-1 focus:ring-[#BF4129] placeholder-neutral-500 resize-vertical min-h-[120px]"
                  placeholder="Type your message here..."
                  rows={6}
                  required
                />
              </div>

              <button
                type="submit"
                className="h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 px-8"
              >
                Send Message
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-[#262626] rounded-2xl p-10 text-center max-w-md w-full">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <div className="text-white text-2xl font-bold">✓</div>
            </div>
            <h3 className="text-white text-2xl font-bold font-montserrat mb-4">Thank you!</h3>
            <p className="text-neutral-300 font-montserrat mb-6 leading-relaxed">
              Your message has been received. We'll get back to you shortly.
            </p>
            <button 
              className="h-12 bg-[#BF4129] hover:bg-[#a83a25] border-none rounded-full text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 px-6"
              onClick={handleCloseModal}
            >
              Got it!
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContactUs;
