import TradeChallengeHero from "../components/TradeChallengeHero";
import TradeChallengeTracker from "../components/TradeChallengeTracker";
import TradeChallengeHowItWorks from "../components/TradeChallengeHowItWorks";
import TradeChallengeCallToAction from "../components/TradeChallengeCallToAction";
import Header from "../components/Header";
import Footer from "../components/Footer";

export default function TradeChallengePage() {
  return (
    <div className="w-full bg-neutral-900">
      <Header />
      <main className="relative w-full bg-neutral-900">
        <TradeChallengeHero />
        <TradeChallengeTracker />
        <TradeChallengeHowItWorks />
        <TradeChallengeCallToAction />
      </main>
      <Footer />
    </div>
  );
}
