import React from 'react';
import { useNavigate } from 'react-router-dom';
import UserDashboardHeader from '../user-dashboard/UserDashboardHeader';
import Footer from '../Footer';
import '../../styles/components/layouts/UserPageLayout.css';

interface UserPageLayoutProps {
  children: React.ReactNode;
  showBlurGradients?: boolean;
  className?: string;
  hideNavAndFooter?: boolean;
  pageTitle?: string;
  pageSubtitle?: string;
  showGreetingSection?: boolean;
  greetingText?: string;
  greetingSubtitle?: string;
  headerControls?: React.ReactNode;
}

const UserPageLayout: React.FC<UserPageLayoutProps> = ({
  children,
  showBlurGradients = true,
  className = '',
  hideNavAndFooter = false,
  pageTitle,
  pageSubtitle,
  showGreetingSection = false,
  greetingText = "Hi, Oluwatosin 👋",
  greetingSubtitle = "Here is your overview",
  headerControls
}) => {
  const navigate = useNavigate();

  return (
    <div className={`user-page-layout-container ${className}`}>
      {/* Background Blur Gradients */}
      {showBlurGradients && (
        <>
          <div className="blur-gradient blur-gradient-1"></div>
          <div className="blur-gradient blur-gradient-2"></div>
          <div className="blur-gradient blur-gradient-3"></div>
          <div className="blur-gradient blur-gradient-4"></div>
          <div className="blur-gradient blur-gradient-5"></div>
        </>
      )}

      {/* Header */}
      {!hideNavAndFooter && (
        <UserDashboardHeader
          onNavigateToLanding={() => navigate('/')}
        />
      )}

      {/* Main Content */}
      <main className="user-page-main">
        <div className="user-page-content">
          {/* Optional Page Title Section */}
          {pageTitle && (
            <div className="page-header">
              <h1 className="page-title">{pageTitle}</h1>
              {pageSubtitle && (
                <p className="page-subtitle">{pageSubtitle}</p>
              )}
            </div>
          )}

          {/* Optional Greeting Section */}
          {showGreetingSection && (
            <div className="user-header">
              <div className="user-greeting">
                <h1 className="greeting-text">{greetingText}</h1>
                <p className="greeting-subtitle">{greetingSubtitle}</p>
              </div>
              {headerControls && (
                <div className="header-controls">
                  {headerControls}
                </div>
              )}
            </div>
          )}

          {/* Page Content */}
          <div className="page-body">
            {children}
          </div>
        </div>
      </main>

      {/* Footer */}
      {!hideNavAndFooter && <Footer />}
    </div>
  );
};

export default UserPageLayout;
