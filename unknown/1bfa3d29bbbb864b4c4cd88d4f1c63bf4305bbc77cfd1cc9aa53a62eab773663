import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/ComparisonTable.css';

const ComparisonTable: React.FC = () => {
  const navigate = useNavigate();
  const [activeView, setActiveView] = useState<'traditional' | 'esvc'>('traditional');

  const handleStartStaking = () => {
    // Navigate to My Stake page in user dashboard
    navigate('/user-dashboard/my-stake');
  };

  const handleSeeHowItWorks = () => {
    // Navigate to login page to see how it works
    navigate('/login');
  };

  const features = [
    {
      traditional: 'Speculative Trading',
      esvc: 'Token-gated startup funding'
    },
    {
      traditional: 'No revenue backing',
      esvc: 'Backed by real assets'
    },
    {
      traditional: 'Opaque Tokenomics',
      esvc: 'Transparent treasury'
    },
    {
      traditional: 'Low staking rewards',
      esvc: 'High-yield ESVC staking'
    }
  ];

  return (
    <section className="comparison-table">
      <div className="container">
        <div className="comparison-header">
          <h2 className="comparison-title">What Makes Us Different</h2>
          <p className="comparison-subtitle">ESVC Staking Versus Traditional Crypto</p>

          {/* Toggle Switch */}
          <div className="comparison-toggle">
            <span className={`toggle-option ${activeView === 'traditional' ? 'active' : ''}`}>
              Traditional Crypto
            </span>
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={activeView === 'esvc'}
                onChange={() => setActiveView(activeView === 'traditional' ? 'esvc' : 'traditional')}
              />
              <span className="toggle-slider"></span>
            </label>
            <span className={`toggle-option ${activeView === 'esvc' ? 'active' : ''}`}>
              ESVC
            </span>
          </div>
        </div>

        <div className="comparison-content">
          <div className={`comparison-grid ${activeView}`}>
            {activeView === 'traditional' ? (
              <>
                <div className="comparison-header-single traditional">
                  <h3>Traditional Crypto</h3>
                </div>
                {/* Features for Traditional Crypto */}
                {features.map((feature, index) => (
                  <div key={index} className="feature-item traditional single">
                    <span className="feature-icon">💰</span>
                    <span className="feature-text">{feature.traditional}</span>
                  </div>
                ))}
              </>
            ) : activeView === 'esvc' ? (
              <>
                <div className="comparison-header-single esvc">
                  <h3>ESVC</h3>
                </div>
                {/* Features for ESVC */}
                {features.map((feature, index) => (
                  <div key={index} className="feature-item esvc single">
                    <span className="feature-icon">💰</span>
                    <span className="feature-text">{feature.esvc}</span>
                  </div>
                ))}
              </>
            ) : (
              <>
                {/* Default: Show both columns */}
                <div className="comparison-header-left">
                  <h3>Traditional Crypto</h3>
                </div>
                <div className="comparison-header-right">
                  <h3>ESVC</h3>
                </div>
                {/* Features */}
                {features.map((feature, index) => (
                  <React.Fragment key={index}>
                    <div className="feature-item traditional">
                      <span className="feature-icon">💰</span>
                      <span className="feature-text">{feature.traditional}</span>
                    </div>
                    <div className="feature-item esvc">
                      <span className="feature-icon">💰</span>
                      <span className="feature-text">{feature.esvc}</span>
                    </div>
                  </React.Fragment>
                ))}
              </>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="comparison-actions">
          <button className="btn-primary" onClick={handleStartStaking}>
            Start Staking Now
            <span className="cta-icon">🚀</span>
          </button>
          <button className="btn-secondary" onClick={handleSeeHowItWorks}>
            See How It Works
            <span className="arrow">→</span>
          </button>
        </div>
      </div>
    </section>
  );
};

export default ComparisonTable;
